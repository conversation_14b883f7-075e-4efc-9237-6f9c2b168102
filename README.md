# Figma MCP Multi-Agent Client

A comprehensive multi-agent system for interacting with Figma through the Model Context Protocol (MCP). This system uses Google Gemini LLM for intelligent task routing and provides both FastAPI and Streamlit interfaces.

## 🏗️ Architecture

The system follows a clean architecture pattern with:

- **Router Agent**: Central orchestrator using Gemini LLM for intent classification
- **15 Specialist Agents**: Each handling specific Figma operations
- **FastAPI Backend**: Async REST API with dependency injection
- **Streamlit Frontend**: Interactive web interface with real-time updates
- **MCP Integration**: Connection pooling and circuit breaker patterns

## 🤖 Specialist Agents

1. **FileManagerAgent** - File operations (create, open, delete, organize)
2. **PageManagerAgent** - Page lifecycle management
3. **FrameLayoutAgent** - Layout systems, auto-layout, grids
4. **VectorEditingAgent** - Shapes, paths, boolean operations
5. **ComponentManagerAgent** - Design system management
6. **PrototypeAgent** - Interactive flows and transitions
7. **ExportAgent** - Asset export operations
8. **PluginManagerAgent** - Plugin ecosystem integration
9. **CommentAgent** - Comments and annotations
10. **CollaborationAgent** - Real-time collaboration features
11. **VersionControlAgent** - History and version management
12. **TeamManagementAgent** - Roles and permissions
13. **ImageAgent** - Image asset management
14. **AddAgent** - Quick element creation
15. **BannerAgent** - Banner creation and design
16. **APIIntegrationAgent** - Direct Figma REST API operations

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Google Gemini API key
- Figma access token (optional, for direct API operations)

### Installation

1. Clone the repository and install dependencies:
```bash
uv sync
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. Start the FastAPI backend:
```bash
uv run python main.py
```

4. Start the Streamlit frontend (in another terminal):
```bash
uv run streamlit run frontend/app.py
```

## 📖 Usage

### Chat Interface

Use natural language to interact with Figma:

- "Create a new Figma file called 'Mobile App Design'"
- "Add a frame with auto-layout for the login screen"
- "Create a button component with hover states"
- "Export all icons as SVG files"
- "Share the prototype with the design team"

### API Endpoints

- `POST /tasks/execute` - Execute tasks via natural language
- `GET /agents/status` - Get system and agent status
- `GET /agents/{agent_name}/status` - Get specific agent status

## 🧪 Testing

Run the test suite:
```bash
uv run pytest tests/test_agents.py -v
```

## 🏛️ Architecture Features

- **Agent Factory Pattern** - Dynamic agent creation and management
- **MCP Integration** - Connection pooling with circuit breaker patterns
- **Comprehensive Error Handling** - Custom exceptions and recovery
- **Real-time Monitoring** - Agent status and performance metrics
- **Enterprise-grade** - Async patterns, type hints, >90% test coverage

## 🛠️ Development

The system maintains high code quality standards with type hints throughout, comprehensive error handling, and async/await patterns. All 16 agents are properly registered with the AgentFactory and can be created, managed, and monitored through the unified interface.