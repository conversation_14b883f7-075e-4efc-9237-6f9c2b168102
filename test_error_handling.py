#!/usr/bin/env python3
"""
Test script to verify improved MCP connection error handling.
"""

import httpx
import json

def test_error_scenarios():
    """Test different error scenarios to verify error handling."""
    print("🧪 TESTING MCP CONNECTION ERROR HANDLING")
    print("=" * 60)
    
    client = httpx.Client(timeout=30.0)
    
    # Test scenarios that should produce specific error messages
    test_scenarios = [
        {
            "name": "nonexistent-command",
            "config": {
                "name": "test-nonexistent",
                "command": "nonexistent_command_12345",
                "args": ["--help"],
                "env": {},
                "description": "Test nonexistent command"
            },
            "expected_error_type": "Command not found"
        },
        {
            "name": "invalid-file-path",
            "config": {
                "name": "test-invalid-path",
                "command": "node",
                "args": ["/nonexistent/path/server.js"],
                "env": {},
                "description": "Test invalid file path"
            },
            "expected_error_type": "File not found"
        },
        {
            "name": "non-mcp-python-script",
            "config": {
                "name": "test-non-mcp",
                "command": "python",
                "args": ["-c", "import time; time.sleep(1); print('Not an MCP server')"],
                "env": {},
                "description": "Test non-MCP Python process"
            },
            "expected_error_type": "Not a valid MCP server"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. Testing: {scenario['name']}")
        print(f"   Expected: {scenario['expected_error_type']}")
        print(f"   Command: {scenario['config']['command']} {' '.join(scenario['config']['args'])}")
        
        try:
            response = client.post(
                "http://localhost:8000/mcp/servers/connect",
                json={"server_config": scenario['config']}
            )
            
            result = response.json()
            
            if response.status_code == 200:
                if result.get('success'):
                    print("   ⚠️ Unexpected success - this should have failed")
                else:
                    error_msg = result.get('message', '')
                    print(f"   ✅ Failed as expected: {error_msg}")
                    
                    # Check if the error message contains expected keywords
                    expected_keywords = {
                        "Command not found": ["not found", "Command"],
                        "File not found": ["File not found", "No such file"],
                        "Not a valid MCP server": ["initialization timed out", "not responding", "not a valid MCP server"]
                    }
                    
                    expected_type = scenario['expected_error_type']
                    if expected_type in expected_keywords:
                        keywords = expected_keywords[expected_type]
                        if any(keyword in error_msg for keyword in keywords):
                            print(f"   ✅ Error message contains expected keywords")
                        else:
                            print(f"   ⚠️ Error message doesn't contain expected keywords: {keywords}")
            else:
                print(f"   ❌ HTTP Error {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_successful_connection_simulation():
    """Test what a successful connection would look like."""
    print(f"\n🎯 TESTING SUCCESSFUL CONNECTION PATTERNS")
    print("=" * 60)
    
    print("✅ For a successful MCP connection, you would typically use:")
    print("   1. **Figma MCP Server (if available):**")
    print("      - Command: bun")
    print("      - Args: path/to/figma-mcp-server.ts")
    print("      - Env: FIGMA_ACCESS_TOKEN=your_token")
    print()
    print("   2. **NPX Package (if npm is available):**")
    print("      - Command: npx")
    print("      - Args: @figma/mcp-server")
    print("      - Env: FIGMA_ACCESS_TOKEN=your_token")
    print()
    print("   3. **Local MCP Server:**")
    print("      - Command: node")
    print("      - Args: path/to/your-mcp-server.js")
    print("      - Env: Any required environment variables")
    print()
    print("💡 **Key Point:** The command must start a process that implements")
    print("   the MCP (Model Context Protocol) and responds to initialization messages.")

def main():
    """Run error handling tests."""
    test_error_scenarios()
    test_successful_connection_simulation()
    
    print(f"\n📋 SUMMARY")
    print("=" * 60)
    print("✅ Error handling improvements implemented:")
    print("   - Better error messages for command not found")
    print("   - Specific handling for file path issues")
    print("   - Timeout detection for non-MCP processes")
    print("   - Clear guidance for users on what constitutes a valid MCP server")
    print()
    print("🎯 **Next Steps for Users:**")
    print("   1. Install a real MCP server (like Figma MCP server)")
    print("   2. Use the improved error messages to troubleshoot connection issues")
    print("   3. Check the frontend for helpful tips and guidance")

if __name__ == "__main__":
    main()
