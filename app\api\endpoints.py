"""
FastAPI endpoints for the Figma MCP client.

This module provides REST API endpoints with async support, dependency injection,
error handling, and connection pooling for the multi-agent system.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from ..core.config import settings
from ..core.exceptions import FigmaMCPError, AgentError, MCPConnectionError
from ..core.mcp_client import mcp_client_manager
from ..agents import AgentFactory, RouterAgent, AgentTask, AgentResponse


logger = logging.getLogger(__name__)


# Pydantic models for API requests/responses
class TaskRequest(BaseModel):
    """Request model for task execution."""
    query: str = Field(..., description="Natural language query for the task")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context")
    timeout: Optional[float] = Field(default=None, description="Task timeout in seconds")


class TaskResponse(BaseModel):
    """Response model for task execution."""
    success: bool
    task_id: str
    result: Optional[Any] = None
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    agent_used: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentStatusResponse(BaseModel):
    """Response model for agent status."""
    name: str
    description: str
    is_busy: bool
    current_task: Optional[str] = None
    capabilities: List[str]
    total_tasks: int
    successful_tasks: int
    failed_tasks: int


class SystemStatusResponse(BaseModel):
    """Response model for system status."""
    total_agents: int
    active_agents: int
    busy_agents: int
    mcp_connections: Dict[str, Any]
    agents: Dict[str, AgentStatusResponse]


class MCPServerConfig(BaseModel):
    """Model for MCP server configuration."""
    name: str = Field(..., description="Unique name for the MCP server")
    command: str = Field(..., description="Command to start the MCP server")
    args: List[str] = Field(default_factory=list, description="Arguments for the server command")
    env: Optional[Dict[str, str]] = Field(default_factory=dict, description="Environment variables")
    description: Optional[str] = Field(None, description="Description of the MCP server")


class MCPServerStatus(BaseModel):
    """Model for MCP server status."""
    name: str
    is_connected: bool
    is_healthy: bool
    tools_count: int
    last_ping: Optional[str] = None
    error_message: Optional[str] = None


class MCPConnectionRequest(BaseModel):
    """Request model for connecting to MCP server."""
    server_config: MCPServerConfig


class MCPConnectionResponse(BaseModel):
    """Response model for MCP connection operations."""
    success: bool
    message: str
    server_name: Optional[str] = None
    tools: Optional[List[Dict[str, Any]]] = None


class ChatMessage(BaseModel):
    """Model for chat messages."""
    role: str = Field(..., description="Role: 'user', 'assistant', or 'system'")
    content: str = Field(..., description="Message content")
    timestamp: Optional[str] = Field(None, description="Message timestamp")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="Tool calls made")
    tool_results: Optional[List[Dict[str, Any]]] = Field(None, description="Tool execution results")


class ChatRequest(BaseModel):
    """Request model for chat interactions."""
    message: str = Field(..., description="User message")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for context")
    use_mcp_tools: bool = Field(True, description="Whether to use available MCP tools")


class ChatResponse(BaseModel):
    """Response model for chat interactions."""
    response: str
    conversation_id: str
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_results: Optional[List[Dict[str, Any]]] = None
    available_tools: Optional[List[str]] = None


# Dependency injection functions
async def get_router_agent() -> RouterAgent:
    """Get or create router agent instance."""
    try:
        router = AgentFactory.get_agent("RouterAgent")
        if not router:
            logger.info("Creating RouterAgent on demand")
            router = AgentFactory.create_agent("RouterAgent")
        return router
    except Exception as e:
        logger.error(f"Failed to get/create RouterAgent: {e}")
        raise HTTPException(status_code=500, detail=f"Router agent unavailable: {str(e)}")


async def get_mcp_client_manager():
    """Get MCP client manager instance."""
    return mcp_client_manager


# Application lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application startup and shutdown."""
    logger.info("Starting Figma MCP Client API")

    # Initialize agents lazily - don't create all at startup to avoid blocking
    try:
        logger.info("Agent factory ready - agents will be created on demand")
        logger.info(f"Available agent classes: {list(AgentFactory.list_available_agents())}")

    except Exception as e:
        logger.error(f"Failed to initialize agent system: {e}")
        raise

    yield

    # Cleanup
    logger.info("Shutting down Figma MCP Client API")
    try:
        await mcp_client_manager.close_all_connections()
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")


# Create FastAPI application
app = FastAPI(
    title="Figma MCP Multi-Agent Client",
    description="Multi-agent system for Figma operations through Model Context Protocol",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Exception handlers
@app.exception_handler(FigmaMCPError)
async def figma_mcp_exception_handler(request, exc: FigmaMCPError):
    """Handle Figma MCP specific exceptions."""
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=exc.to_dict()
    )


@app.exception_handler(AgentError)
async def agent_exception_handler(request, exc: AgentError):
    """Handle agent-specific exceptions."""
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=exc.to_dict()
    )


@app.exception_handler(MCPConnectionError)
async def mcp_connection_exception_handler(request, exc: MCPConnectionError):
    """Handle MCP connection exceptions."""
    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content=exc.to_dict()
    )


# API Routes
@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information."""
    return {
        "name": "Figma MCP Multi-Agent Client",
        "version": "0.1.0",
        "description": "Multi-agent system for Figma operations through Model Context Protocol"
    }


@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "figma-mcp-client"}


@app.post("/tasks/execute", response_model=TaskResponse)
async def execute_task(
    task_request: TaskRequest,
    background_tasks: BackgroundTasks,
    router_agent: RouterAgent = Depends(get_router_agent)
):
    """
    Execute a task using the multi-agent system.
    
    This endpoint accepts natural language queries and routes them to
    appropriate specialist agents through the router agent.
    """
    try:
        # Create agent task
        agent_task = AgentTask(
            task_type="route_and_execute",
            description=task_request.query,
            parameters={"query": task_request.query},
            context=task_request.context
        )
        
        # Execute task with timeout
        response = await router_agent.execute_with_timeout(
            agent_task,
            timeout=task_request.timeout
        )
        
        return TaskResponse(
            success=response.success,
            task_id=agent_task.task_id,
            result=response.result,
            error_message=response.error_message,
            execution_time=response.execution_time,
            agent_used="RouterAgent",
            metadata=response.metadata
        )
        
    except Exception as e:
        logger.error(f"Task execution failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task execution failed: {str(e)}"
        )


@app.get("/agents/status", response_model=SystemStatusResponse)
async def get_system_status():
    """Get overall system status including all agents."""
    try:
        agent_summary = AgentFactory.get_agent_status_summary()
        mcp_stats = mcp_client_manager.get_connection_stats()
        
        agents_status = {}
        for agent_name in AgentFactory.list_active_agents():
            agent = AgentFactory.get_agent(agent_name)
            if agent:
                status_data = agent.get_status()
                agents_status[agent_name] = AgentStatusResponse(**status_data)
        
        return SystemStatusResponse(
            total_agents=agent_summary["total_active"],
            active_agents=agent_summary["total_active"],
            busy_agents=agent_summary["busy_agents"],
            mcp_connections=mcp_stats,
            agents=agents_status
        )
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system status: {str(e)}"
        )


@app.get("/agents/{agent_name}/status", response_model=AgentStatusResponse)
async def get_agent_status(agent_name: str):
    """Get status of a specific agent."""
    try:
        agent = AgentFactory.get_agent(agent_name)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_name} not found"
            )
        
        status_data = agent.get_status()
        return AgentStatusResponse(**status_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent status: {str(e)}"
        )


@app.get("/agents/{agent_name}/history")
async def get_agent_history(agent_name: str, limit: int = 10):
    """Get task history for a specific agent."""
    try:
        agent = AgentFactory.get_agent(agent_name)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_name} not found"
            )
        
        history = agent.get_task_history(limit=limit)
        return {"agent": agent_name, "history": history}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent history: {str(e)}"
        )


@app.get("/mcp/connections")
async def get_mcp_connections(
    mcp_manager = Depends(get_mcp_client_manager)
):
    """Get MCP connection statistics."""
    try:
        stats = mcp_manager.get_connection_stats()
        return {"mcp_connections": stats}

    except Exception as e:
        logger.error(f"Failed to get MCP connections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get MCP connections: {str(e)}"
        )


@app.get("/mcp/servers", response_model=List[MCPServerStatus])
async def list_mcp_servers(mcp_manager = Depends(get_mcp_client_manager)):
    """List all configured MCP servers and their status."""
    try:
        servers = []
        connections = mcp_manager.connections

        for server_name, connection in connections.items():
            # Get tools count
            tools_count = 0
            try:
                if connection.session:
                    tools = await connection.session.list_tools()
                    tools_count = len(tools.tools) if tools.tools else 0
            except Exception:
                tools_count = 0

            servers.append(MCPServerStatus(
                name=server_name,
                is_connected=connection.session is not None,
                is_healthy=connection.is_healthy,
                tools_count=tools_count,
                last_ping=connection.last_ping.isoformat() if hasattr(connection, 'last_ping') and connection.last_ping else None,
                error_message=getattr(connection, 'last_error', None)
            ))

        return servers
    except Exception as e:
        logger.error(f"Failed to list MCP servers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list MCP servers: {str(e)}"
        )


@app.post("/mcp/servers/connect", response_model=MCPConnectionResponse)
async def connect_mcp_server(
    request: MCPConnectionRequest,
    mcp_manager = Depends(get_mcp_client_manager)
):
    """Connect to a new MCP server."""
    try:
        server_config = request.server_config

        # Create server parameters
        from mcp import StdioServerParameters
        server_params = StdioServerParameters(
            command=server_config.command,
            args=server_config.args,
            env=server_config.env or {}
        )

        # Connect to the server
        async with mcp_manager.get_connection(server_config.name, server_params) as session:
            # Test the connection by listing tools
            tools_result = await session.list_tools()
            tools = [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema.model_dump() if tool.inputSchema else None
                }
                for tool in (tools_result.tools or [])
            ]

            return MCPConnectionResponse(
                success=True,
                message=f"Successfully connected to MCP server '{server_config.name}'",
                server_name=server_config.name,
                tools=tools
            )

    except Exception as e:
        logger.error(f"Failed to connect to MCP server {request.server_config.name}: {e}")
        return MCPConnectionResponse(
            success=False,
            message=f"Failed to connect to MCP server: {str(e)}",
            server_name=request.server_config.name
        )


@app.delete("/mcp/servers/{server_name}")
async def disconnect_mcp_server(
    server_name: str,
    mcp_manager = Depends(get_mcp_client_manager)
):
    """Disconnect from an MCP server."""
    try:
        await mcp_manager.close_connection(server_name)
        return {"success": True, "message": f"Disconnected from server '{server_name}'"}
    except Exception as e:
        logger.error(f"Failed to disconnect from MCP server {server_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to disconnect from server: {str(e)}"
        )


@app.get("/mcp/servers/{server_name}/tools")
async def get_mcp_server_tools(
    server_name: str,
    mcp_manager = Depends(get_mcp_client_manager)
):
    """Get available tools from a specific MCP server."""
    try:
        if server_name not in mcp_manager.connections:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"MCP server '{server_name}' not found"
            )

        connection = mcp_manager.connections[server_name]
        if not connection.session:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"MCP server '{server_name}' is not connected"
            )

        tools_result = await connection.session.list_tools()
        tools = [
            {
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.inputSchema.model_dump() if tool.inputSchema else None
            }
            for tool in (tools_result.tools or [])
        ]

        return {"server_name": server_name, "tools": tools}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get tools from MCP server {server_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tools from server: {str(e)}"
        )


@app.post("/chat", response_model=ChatResponse)
async def chat_with_mcp(
    request: ChatRequest,
    router_agent = Depends(get_router_agent),
    mcp_manager = Depends(get_mcp_client_manager)
):
    """Chat interface that can use MCP tools."""
    try:
        import uuid
        from datetime import datetime

        # Generate conversation ID if not provided
        conversation_id = request.conversation_id or str(uuid.uuid4())

        # Get available MCP tools
        available_tools = []
        tool_calls = []
        tool_results = []

        if request.use_mcp_tools:
            for server_name, connection in mcp_manager.connections.items():
                if connection.session and connection.is_healthy:
                    try:
                        tools_result = await connection.session.list_tools()
                        for tool in (tools_result.tools or []):
                            available_tools.append(f"{server_name}.{tool.name}")
                    except Exception as e:
                        logger.warning(f"Failed to get tools from {server_name}: {e}")

        # Create enhanced task with MCP context
        enhanced_query = f"""
        User message: {request.message}

        Available MCP tools: {', '.join(available_tools) if available_tools else 'None'}

        Please respond naturally to the user's message. If the message requires using any of the available MCP tools,
        indicate which tools should be used and how. Provide a helpful and conversational response.
        """

        # Execute task through router agent
        task = AgentTask(
            query=enhanced_query,
            context={
                "conversation_id": conversation_id,
                "available_mcp_tools": available_tools,
                "use_mcp_tools": request.use_mcp_tools,
                "original_message": request.message
            },
            timeout=30.0
        )

        result = await router_agent.execute_task(task)

        # For now, we'll return the agent's response
        # In a full implementation, you'd parse the response to determine if MCP tools should be called
        response_text = result.result if result.success else f"I encountered an error: {result.error_message}"

        return ChatResponse(
            response=response_text,
            conversation_id=conversation_id,
            tool_calls=tool_calls,
            tool_results=tool_results,
            available_tools=available_tools
        )

    except Exception as e:
        logger.error(f"Chat request failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat request failed: {str(e)}"
        )


@app.get("/chat/tools")
async def get_available_chat_tools(mcp_manager = Depends(get_mcp_client_manager)):
    """Get all available MCP tools for chat interface."""
    try:
        all_tools = {}

        for server_name, connection in mcp_manager.connections.items():
            if connection.session and connection.is_healthy:
                try:
                    tools_result = await connection.session.list_tools()
                    server_tools = []

                    for tool in (tools_result.tools or []):
                        server_tools.append({
                            "name": tool.name,
                            "description": tool.description,
                            "full_name": f"{server_name}.{tool.name}",
                            "input_schema": tool.inputSchema.model_dump() if tool.inputSchema else None
                        })

                    all_tools[server_name] = server_tools

                except Exception as e:
                    logger.warning(f"Failed to get tools from {server_name}: {e}")
                    all_tools[server_name] = []

        return {"tools_by_server": all_tools}

    except Exception as e:
        logger.error(f"Failed to get available chat tools: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get available tools: {str(e)}"
        )


# Router instance for external use
router = app
