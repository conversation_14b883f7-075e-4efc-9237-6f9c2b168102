"""
FastAPI endpoints for the Figma MCP client.

This module provides REST API endpoints with async support, dependency injection,
error handling, and connection pooling for the multi-agent system.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from ..core.config import settings
from ..core.exceptions import FigmaMCPError, AgentError, MCPConnectionError
from ..core.mcp_client import mcp_client_manager
from ..agents import AgentFactory, RouterAgent, AgentTask, AgentResponse


logger = logging.getLogger(__name__)


# Pydantic models for API requests/responses
class TaskRequest(BaseModel):
    """Request model for task execution."""
    query: str = Field(..., description="Natural language query for the task")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional context")
    timeout: Optional[float] = Field(default=None, description="Task timeout in seconds")


class TaskResponse(BaseModel):
    """Response model for task execution."""
    success: bool
    task_id: str
    result: Optional[Any] = None
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    agent_used: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentStatusResponse(BaseModel):
    """Response model for agent status."""
    name: str
    description: str
    is_busy: bool
    current_task: Optional[str] = None
    capabilities: List[str]
    total_tasks: int
    successful_tasks: int
    failed_tasks: int


class SystemStatusResponse(BaseModel):
    """Response model for system status."""
    total_agents: int
    active_agents: int
    busy_agents: int
    mcp_connections: Dict[str, Any]
    agents: Dict[str, AgentStatusResponse]


# Dependency injection functions
async def get_router_agent() -> RouterAgent:
    """Get or create router agent instance."""
    try:
        router = AgentFactory.get_agent("RouterAgent")
        if not router:
            logger.info("Creating RouterAgent on demand")
            router = AgentFactory.create_agent("RouterAgent")
        return router
    except Exception as e:
        logger.error(f"Failed to get/create RouterAgent: {e}")
        raise HTTPException(status_code=500, detail=f"Router agent unavailable: {str(e)}")


async def get_mcp_client_manager():
    """Get MCP client manager instance."""
    return mcp_client_manager


# Application lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application startup and shutdown."""
    logger.info("Starting Figma MCP Client API")

    # Initialize agents lazily - don't create all at startup to avoid blocking
    try:
        logger.info("Agent factory ready - agents will be created on demand")
        logger.info(f"Available agent classes: {list(AgentFactory.list_available_agents())}")

    except Exception as e:
        logger.error(f"Failed to initialize agent system: {e}")
        raise

    yield

    # Cleanup
    logger.info("Shutting down Figma MCP Client API")
    try:
        await mcp_client_manager.close_all_connections()
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")


# Create FastAPI application
app = FastAPI(
    title="Figma MCP Multi-Agent Client",
    description="Multi-agent system for Figma operations through Model Context Protocol",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Exception handlers
@app.exception_handler(FigmaMCPError)
async def figma_mcp_exception_handler(request, exc: FigmaMCPError):
    """Handle Figma MCP specific exceptions."""
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=exc.to_dict()
    )


@app.exception_handler(AgentError)
async def agent_exception_handler(request, exc: AgentError):
    """Handle agent-specific exceptions."""
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=exc.to_dict()
    )


@app.exception_handler(MCPConnectionError)
async def mcp_connection_exception_handler(request, exc: MCPConnectionError):
    """Handle MCP connection exceptions."""
    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content=exc.to_dict()
    )


# API Routes
@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information."""
    return {
        "name": "Figma MCP Multi-Agent Client",
        "version": "0.1.0",
        "description": "Multi-agent system for Figma operations through Model Context Protocol"
    }


@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "figma-mcp-client"}


@app.post("/tasks/execute", response_model=TaskResponse)
async def execute_task(
    task_request: TaskRequest,
    background_tasks: BackgroundTasks,
    router_agent: RouterAgent = Depends(get_router_agent)
):
    """
    Execute a task using the multi-agent system.
    
    This endpoint accepts natural language queries and routes them to
    appropriate specialist agents through the router agent.
    """
    try:
        # Create agent task
        agent_task = AgentTask(
            task_type="route_and_execute",
            description=task_request.query,
            parameters={"query": task_request.query},
            context=task_request.context
        )
        
        # Execute task with timeout
        response = await router_agent.execute_with_timeout(
            agent_task,
            timeout=task_request.timeout
        )
        
        return TaskResponse(
            success=response.success,
            task_id=agent_task.task_id,
            result=response.result,
            error_message=response.error_message,
            execution_time=response.execution_time,
            agent_used="RouterAgent",
            metadata=response.metadata
        )
        
    except Exception as e:
        logger.error(f"Task execution failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task execution failed: {str(e)}"
        )


@app.get("/agents/status", response_model=SystemStatusResponse)
async def get_system_status():
    """Get overall system status including all agents."""
    try:
        agent_summary = AgentFactory.get_agent_status_summary()
        mcp_stats = mcp_client_manager.get_connection_stats()
        
        agents_status = {}
        for agent_name in AgentFactory.list_active_agents():
            agent = AgentFactory.get_agent(agent_name)
            if agent:
                status_data = agent.get_status()
                agents_status[agent_name] = AgentStatusResponse(**status_data)
        
        return SystemStatusResponse(
            total_agents=agent_summary["total_active"],
            active_agents=agent_summary["total_active"],
            busy_agents=agent_summary["busy_agents"],
            mcp_connections=mcp_stats,
            agents=agents_status
        )
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system status: {str(e)}"
        )


@app.get("/agents/{agent_name}/status", response_model=AgentStatusResponse)
async def get_agent_status(agent_name: str):
    """Get status of a specific agent."""
    try:
        agent = AgentFactory.get_agent(agent_name)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_name} not found"
            )
        
        status_data = agent.get_status()
        return AgentStatusResponse(**status_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent status: {str(e)}"
        )


@app.get("/agents/{agent_name}/history")
async def get_agent_history(agent_name: str, limit: int = 10):
    """Get task history for a specific agent."""
    try:
        agent = AgentFactory.get_agent(agent_name)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Agent {agent_name} not found"
            )
        
        history = agent.get_task_history(limit=limit)
        return {"agent": agent_name, "history": history}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get agent history: {str(e)}"
        )


@app.get("/mcp/connections")
async def get_mcp_connections(
    mcp_manager = Depends(get_mcp_client_manager)
):
    """Get MCP connection statistics."""
    try:
        stats = mcp_manager.get_connection_stats()
        return {"mcp_connections": stats}
        
    except Exception as e:
        logger.error(f"Failed to get MCP connections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get MCP connections: {str(e)}"
        )


# Router instance for external use
router = app
