#!/usr/bin/env python3
"""
Debug script to identify any import issues in the application.
"""

import sys
import os
import traceback

def debug_imports():
    """Debug all imports systematically."""
    
    print("🔍 Debugging Import Issues")
    print("=" * 50)
    
    # Check current working directory
    print(f"\n📁 Current working directory: {os.getcwd()}")
    print(f"🐍 Python path: {sys.path[:3]}...")  # Show first 3 entries
    
    # Check if app directory exists
    app_dir = os.path.join(os.getcwd(), 'app')
    print(f"📂 App directory exists: {os.path.exists(app_dir)}")
    
    if os.path.exists(app_dir):
        print(f"📋 App directory contents: {os.listdir(app_dir)}")
    
    # Test imports step by step
    print("\n🧪 Testing imports step by step...")
    
    # Test 1: Import app module
    try:
        import app
        print("   ✅ import app - SUCCESS")
        print(f"   📍 app.__file__: {app.__file__}")
    except Exception as e:
        print(f"   ❌ import app - FAILED: {e}")
        traceback.print_exc()
        return False
    
    # Test 2: Import app.core
    try:
        import app.core
        print("   ✅ import app.core - SUCCESS")
        print(f"   📍 app.core.__file__: {app.core.__file__}")
    except Exception as e:
        print(f"   ❌ import app.core - FAILED: {e}")
        traceback.print_exc()
        return False
    
    # Test 3: Import specific modules
    try:
        from app.core.config import settings
        print("   ✅ from app.core.config import settings - SUCCESS")
        print(f"   🔧 API Host: {settings.api_host}")
    except Exception as e:
        print(f"   ❌ from app.core.config import settings - FAILED: {e}")
        traceback.print_exc()
        return False
    
    # Test 4: Import agents
    try:
        from app.agents import AgentFactory
        print("   ✅ from app.agents import AgentFactory - SUCCESS")
    except Exception as e:
        print(f"   ❌ from app.agents import AgentFactory - FAILED: {e}")
        traceback.print_exc()
        return False
    
    # Test 5: Import API endpoints
    try:
        from app.api.endpoints import app as fastapi_app
        print("   ✅ from app.api.endpoints import app - SUCCESS")
    except Exception as e:
        print(f"   ❌ from app.api.endpoints import app - FAILED: {e}")
        traceback.print_exc()
        return False
    
    # Test 6: Import main module
    try:
        import main
        print("   ✅ import main - SUCCESS")
    except Exception as e:
        print(f"   ❌ import main - FAILED: {e}")
        traceback.print_exc()
        return False
    
    # Test 7: Import frontend
    try:
        sys.path.insert(0, os.path.join(os.getcwd(), 'frontend'))
        import app as streamlit_app
        print("   ✅ import frontend.app - SUCCESS")
    except Exception as e:
        print(f"   ❌ import frontend.app - FAILED: {e}")
        # This might fail due to Streamlit dependencies, which is OK
        print("   ℹ️  Frontend import failure might be due to Streamlit-specific imports")
    
    print("\n" + "=" * 50)
    print("✅ Import debugging complete!")
    print("🎯 All critical imports are working correctly")
    
    return True

if __name__ == "__main__":
    debug_imports()
