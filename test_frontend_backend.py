#!/usr/bin/env python3
"""
Test script to verify frontend-backend communication and visualization capabilities.
"""

import httpx
import json
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

def test_frontend_backend_integration():
    """Test that frontend can communicate with backend and create visualizations."""
    
    print("🧪 Testing Frontend-Backend Integration")
    print("=" * 50)
    
    # Test 1: Backend API connectivity
    print("\n1. Testing Backend API Connectivity...")
    try:
        response = httpx.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("   ✅ Backend API is accessible")
            print(f"   📊 Response: {response.json()}")
        else:
            print(f"   ❌ Backend API error: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Backend API connection failed: {e}")
        return False
    
    # Test 2: Agent Status Endpoint
    print("\n2. Testing Agent Status Endpoint...")
    try:
        response = httpx.get("http://localhost:8000/agents/status")
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Agent status endpoint working")
            print(f"   🤖 Total agents: {data.get('total_agents', 0)}")
            print(f"   🟢 Active agents: {data.get('active_agents', 0)}")
            print(f"   🔴 Busy agents: {data.get('busy_agents', 0)}")
            
            # Test visualization creation with real data
            print("\n3. Testing Visualization Creation...")
            
            # Create agent status pie chart
            agents = data.get('agents', {})
            if agents:
                agent_names = list(agents.keys())
                task_counts = [agents[name].get('total_tasks', 0) for name in agent_names]
                
                # Create pie chart
                fig = px.pie(
                    values=task_counts,
                    names=agent_names,
                    title="Tasks by Agent (Test)"
                )
                print("   ✅ Plotly pie chart created successfully")
                
                # Create gauge chart
                total_agents = data.get('total_agents', 0)
                active_agents = data.get('active_agents', 0)
                health_ratio = active_agents / max(total_agents, 1) * 100
                
                fig_gauge = go.Figure(go.Indicator(
                    mode="gauge+number",
                    value=health_ratio,
                    title={"text": "System Health %"},
                    gauge={"axis": {"range": [None, 100]}}
                ))
                print("   ✅ Plotly gauge chart created successfully")
                
                # Create DataFrame for table display
                agent_data = []
                for name, agent_info in agents.items():
                    agent_data.append({
                        "Agent": name,
                        "Status": "Busy" if agent_info.get("is_busy") else "Available",
                        "Total Tasks": agent_info.get("total_tasks", 0),
                        "Capabilities": len(agent_info.get("capabilities", []))
                    })
                
                df = pd.DataFrame(agent_data)
                print("   ✅ Pandas DataFrame created successfully")
                print(f"   📊 DataFrame shape: {df.shape}")
                
            else:
                print("   ⚠️  No agent data available for visualization testing")
                
        else:
            print(f"   ❌ Agent status endpoint error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Agent status test failed: {e}")
        return False
    
    # Test 3: Task Execution (simplified)
    print("\n4. Testing Task Execution Endpoint...")
    try:
        payload = {
            "query": "Test query for system verification",
            "context": {"test": True},
            "timeout": 10.0
        }
        
        response = httpx.post(
            "http://localhost:8000/tasks/execute",
            json=payload,
            timeout=15.0
        )
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ Task execution endpoint responding")
            print(f"   🎯 Task ID: {result.get('task_id', 'N/A')}")
            print(f"   🤖 Agent used: {result.get('agent_used', 'N/A')}")
            print(f"   📊 Success: {result.get('success', False)}")
        else:
            print(f"   ❌ Task execution error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Task execution test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Frontend-Backend Integration Test Complete!")
    print("✅ All visualization dependencies working")
    print("✅ Backend API communication successful")
    print("✅ Plotly charts and gauges functional")
    print("✅ Pandas data handling operational")
    print("🌐 Streamlit frontend ready at: http://localhost:8501")
    print("🔧 FastAPI backend ready at: http://localhost:8000")
    
    return True

if __name__ == "__main__":
    test_frontend_backend_integration()
