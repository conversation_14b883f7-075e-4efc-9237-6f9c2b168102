#!/usr/bin/env python3
"""
Test script for the enhanced MCP interface functionality.
This script demonstrates the new MCP server management and chat capabilities.
"""

import httpx
import json
import time
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000"
TIMEOUT = 30.0

def test_mcp_server_management():
    """Test MCP server management endpoints."""
    print("🔧 Testing MCP Server Management")
    print("=" * 50)
    
    client = httpx.Client(timeout=TIMEOUT)
    
    try:
        # Test 1: List current MCP servers
        print("\n1. Listing current MCP servers...")
        response = client.get(f"{API_BASE_URL}/mcp/servers")
        if response.status_code == 200:
            servers = response.json()
            print(f"✅ Found {len(servers)} MCP servers")
            for server in servers:
                status = "🟢" if server.get("is_healthy") else "🔴" if server.get("is_connected") else "⚪"
                print(f"   {status} {server.get('name')} - {server.get('tools_count', 0)} tools")
        else:
            print(f"❌ Failed to list servers: {response.status_code}")
        
        # Test 2: Test connecting to a mock MCP server
        print("\n2. Testing MCP server connection...")
        mock_server_config = {
            "name": "test-server",
            "command": "echo",
            "args": ["Hello MCP"],
            "env": {"TEST": "true"},
            "description": "Test MCP server for demonstration"
        }
        
        response = client.post(
            f"{API_BASE_URL}/mcp/servers/connect",
            json={"server_config": mock_server_config}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ Successfully connected to {mock_server_config['name']}")
                if result.get("tools"):
                    print(f"   Found {len(result['tools'])} tools")
            else:
                print(f"⚠️ Connection attempt completed but failed: {result.get('message')}")
        else:
            print(f"❌ Failed to connect server: {response.status_code}")
        
        # Test 3: Get available tools
        print("\n3. Getting available chat tools...")
        response = client.get(f"{API_BASE_URL}/chat/tools")
        if response.status_code == 200:
            tools_data = response.json()
            tools_by_server = tools_data.get("tools_by_server", {})
            total_tools = sum(len(tools) for tools in tools_by_server.values())
            print(f"✅ Found {total_tools} total tools across {len(tools_by_server)} servers")
            
            for server_name, tools in tools_by_server.items():
                print(f"   📦 {server_name}: {len(tools)} tools")
                for tool in tools[:3]:  # Show first 3 tools
                    print(f"      • {tool.get('name')} - {tool.get('description', 'No description')[:50]}...")
        else:
            print(f"❌ Failed to get tools: {response.status_code}")
        
    except Exception as e:
        print(f"❌ Error during MCP server testing: {e}")
    finally:
        client.close()

def test_chat_interface():
    """Test the enhanced chat interface."""
    print("\n\n💬 Testing Chat Interface")
    print("=" * 50)
    
    client = httpx.Client(timeout=TIMEOUT)
    
    try:
        # Test chat message
        print("\n1. Sending test chat message...")
        chat_request = {
            "message": "Hello! Can you help me understand what MCP tools are available?",
            "use_mcp_tools": True
        }
        
        response = client.post(f"{API_BASE_URL}/chat", json=chat_request)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat response received:")
            print(f"   Response: {result.get('response', 'No response')[:100]}...")
            print(f"   Conversation ID: {result.get('conversation_id')}")
            print(f"   Available tools: {len(result.get('available_tools', []))}")
            
            if result.get("tool_calls"):
                print(f"   Tool calls made: {len(result['tool_calls'])}")
            
            if result.get("tool_results"):
                print(f"   Tool results: {len(result['tool_results'])}")
                
        else:
            print(f"❌ Chat request failed: {response.status_code}")
            print(f"   Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error during chat testing: {e}")
    finally:
        client.close()

def test_system_status():
    """Test system status with MCP information."""
    print("\n\n📊 Testing System Status")
    print("=" * 50)
    
    client = httpx.Client(timeout=TIMEOUT)
    
    try:
        # Get system status
        response = client.get(f"{API_BASE_URL}/agents/status")
        if response.status_code == 200:
            status = response.json()
            print("✅ System status:")
            print(f"   Active agents: {status.get('active_agents', 0)}")
            print(f"   Total agents: {status.get('total_agents', 0)}")
            
            mcp_connections = status.get('mcp_connections', {})
            if mcp_connections:
                print(f"   MCP connections: {mcp_connections.get('total_connections', 0)}")
                print(f"   Healthy connections: {mcp_connections.get('healthy_connections', 0)}")
        else:
            print(f"❌ Failed to get system status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting system status: {e}")
    finally:
        client.close()

def main():
    """Run all tests."""
    print("🧪 TESTING ENHANCED MCP INTERFACE")
    print("=" * 60)
    print("Testing the new Claude-like MCP interface features...")
    
    # Test each component
    test_system_status()
    test_mcp_server_management()
    test_chat_interface()
    
    print("\n\n🎉 TESTING COMPLETE!")
    print("=" * 60)
    print("✅ Enhanced MCP interface features tested")
    print("🌐 Frontend available at: http://localhost:8501")
    print("🔧 Backend API at: http://localhost:8000")
    print("\n💡 Try the new features:")
    print("   1. Go to 'MCP Servers' page to connect servers")
    print("   2. Use 'Chat Interface' for natural language interaction")
    print("   3. Check sidebar for real-time MCP server status")

if __name__ == "__main__":
    main()
