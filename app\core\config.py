"""
Configuration management for the Figma MCP client.

This module handles all configuration settings using Pydantic Settings
with environment variable support and validation.
"""

from typing import List, Optional
from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Google Gemini API Configuration (Optional)
    gemini_api_key: Optional[str] = Field(default=None, description="Google Gemini API key (optional - enables AI features)")
    gemini_model: str = Field(default="gemini-2.0-flash", description="Gemini model to use")

    # Figma API Configuration (Optional)
    figma_access_token: Optional[str] = Field(default=None, description="Figma access token for direct API calls (optional)")
    
    # MCP Server Configuration
    mcp_server_host: str = Field(default="localhost", description="MCP server host")
    mcp_server_port: int = Field(default=3001, description="MCP server port")
    mcp_connection_timeout: int = Field(default=30, description="MCP connection timeout in seconds")
    mcp_max_connections: int = Field(default=10, description="Maximum MCP connections")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis connection URL")
    
    # FastAPI Configuration
    api_host: str = Field(default="0.0.0.0", description="FastAPI host")
    api_port: int = Field(default=8000, description="FastAPI port")
    api_debug: bool = Field(default=False, description="Enable FastAPI debug mode")
    
    # Streamlit Configuration
    streamlit_host: str = Field(default="localhost", description="Streamlit host")
    streamlit_port: int = Field(default=8501, description="Streamlit port")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    
    # Security Configuration
    secret_key: str = Field(default="dev-secret-key", description="Secret key for security")
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8501"],
        description="CORS allowed origins"
    )
    
    # Performance Configuration
    max_concurrent_agents: int = Field(default=5, description="Maximum concurrent agents")
    agent_timeout: int = Field(default=60, description="Agent timeout in seconds")
    task_queue_size: int = Field(default=100, description="Task queue size")
    
    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()

    @field_validator('log_format')
    @classmethod
    def validate_log_format(cls, v):
        """Validate log format."""
        valid_formats = ['json', 'text']
        if v.lower() not in valid_formats:
            raise ValueError(f'Log format must be one of: {valid_formats}')
        return v.lower()

    @field_validator('mcp_max_connections')
    @classmethod
    def validate_mcp_max_connections(cls, v):
        """Validate MCP max connections."""
        if v < 1 or v > 50:
            raise ValueError('MCP max connections must be between 1 and 50')
        return v

    @field_validator('max_concurrent_agents')
    @classmethod
    def validate_max_concurrent_agents(cls, v):
        """Validate max concurrent agents."""
        if v < 1 or v > 20:
            raise ValueError('Max concurrent agents must be between 1 and 20')
        return v
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )

    @property
    def has_gemini_api_key(self) -> bool:
        """Check if Gemini API key is configured."""
        return bool(self.gemini_api_key and self.gemini_api_key.strip())

    @property
    def has_figma_access_token(self) -> bool:
        """Check if Figma access token is configured."""
        return bool(self.figma_access_token and self.figma_access_token.strip())

    @property
    def ai_features_enabled(self) -> bool:
        """Check if AI features are available."""
        return self.has_gemini_api_key

    @property
    def figma_api_enabled(self) -> bool:
        """Check if direct Figma API features are available."""
        return self.has_figma_access_token


# Global settings instance
settings = Settings()
