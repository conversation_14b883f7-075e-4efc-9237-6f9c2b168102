"""
Configuration management for the Figma MCP client.

This module handles all configuration settings using Pydantic Settings
with environment variable support and validation.
"""

from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Google Gemini API Configuration
    gemini_api_key: str = Field(..., description="Google Gemini API key")
    gemini_model: str = Field(default="gemini-2.0-flash", description="Gemini model to use")
    
    # Figma API Configuration
    figma_access_token: Optional[str] = Field(None, description="Figma access token for direct API calls")
    
    # MCP Server Configuration
    mcp_server_host: str = Field(default="localhost", description="MCP server host")
    mcp_server_port: int = Field(default=3001, description="MCP server port")
    mcp_connection_timeout: int = Field(default=30, description="MCP connection timeout in seconds")
    mcp_max_connections: int = Field(default=10, description="Maximum MCP connections")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis connection URL")
    
    # FastAPI Configuration
    api_host: str = Field(default="0.0.0.0", description="FastAPI host")
    api_port: int = Field(default=8000, description="FastAPI port")
    api_debug: bool = Field(default=False, description="Enable FastAPI debug mode")
    
    # Streamlit Configuration
    streamlit_host: str = Field(default="localhost", description="Streamlit host")
    streamlit_port: int = Field(default=8501, description="Streamlit port")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    
    # Security Configuration
    secret_key: str = Field(..., description="Secret key for security")
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8501"],
        description="CORS allowed origins"
    )
    
    # Performance Configuration
    max_concurrent_agents: int = Field(default=5, description="Maximum concurrent agents")
    agent_timeout: int = Field(default=60, description="Agent timeout in seconds")
    task_queue_size: int = Field(default=100, description="Task queue size")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    @validator('log_format')
    def validate_log_format(cls, v):
        """Validate log format."""
        valid_formats = ['json', 'text']
        if v.lower() not in valid_formats:
            raise ValueError(f'Log format must be one of: {valid_formats}')
        return v.lower()
    
    @validator('mcp_max_connections')
    def validate_mcp_max_connections(cls, v):
        """Validate MCP max connections."""
        if v < 1 or v > 50:
            raise ValueError('MCP max connections must be between 1 and 50')
        return v
    
    @validator('max_concurrent_agents')
    def validate_max_concurrent_agents(cls, v):
        """Validate max concurrent agents."""
        if v < 1 or v > 20:
            raise ValueError('Max concurrent agents must be between 1 and 20')
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()
