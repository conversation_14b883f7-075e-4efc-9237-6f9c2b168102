"""
Frame/Layout Agent for the Figma MCP client.

This agent handles layout systems including frames, auto-layout, grids,
constraints, and responsive design through MCP tools.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
import json

from mcp import StdioServerParameters
from mcp.types import Tool, TextContent

from .base import BaseAgent, AgentTask, AgentResponse, AgentFactory
from ..core.config import settings
from ..core.mcp_client import mcp_client_manager
from ..core.exceptions import AgentError, TaskExecutionError


logger = logging.getLogger(__name__)


class FrameLayoutAgent(BaseAgent):
    """
    Specialist agent for Figma frame and layout operations.
    
    Handles frame creation, auto-layout configuration, grid systems,
    constraints, and responsive design features through the Figma MCP server.
    """
    
    def __init__(self):
        super().__init__(
            name="FrameLayoutAgent",
            description="Manages Figma layout systems including frames, auto-layout, grids, and constraints"
        )
        
        self.capabilities = [
            "frame_creation",
            "auto_layout",
            "grid_systems",
            "constraints",
            "responsive_design",
            "layout_optimization",
            "spacing_management",
            "alignment_tools"
        ]
        
        # MCP server parameters for Figma layout operations
        self.figma_server_params = StdioServerParameters(
            command="figma-mcp-server",
            args=["--layout-operations"],
            env={"FIGMA_ACCESS_TOKEN": settings.figma_access_token} if settings.figma_access_token else None
        )
    
    async def can_handle(self, task: str) -> bool:
        """Check if this agent can handle the given task."""
        layout_operations = [
            "create_frame", "add_frame", "frame",
            "auto_layout", "autolayout", "layout",
            "grid", "layout_grid", "column_grid",
            "constraints", "responsive", "resize",
            "align", "distribute", "spacing",
            "padding", "margin", "gap"
        ]
        
        return any(op in task.lower() for op in layout_operations)
    
    async def get_available_tools(self) -> List[Tool]:
        """Get available MCP tools for layout operations."""
        try:
            async with mcp_client_manager.get_connection(
                "figma-layout-server", 
                self.figma_server_params
            ) as session:
                tools_response = await session.list_tools()
                return tools_response.tools
        except Exception as e:
            logger.error(f"Failed to get layout tools: {e}")
            return []
    
    async def create_frame(
        self,
        file_id: str,
        page_id: str,
        name: str,
        x: float = 0,
        y: float = 0,
        width: float = 375,
        height: float = 812
    ) -> Dict[str, Any]:
        """
        Create a new frame.
        
        Args:
            file_id: ID of the Figma file
            page_id: ID of the page
            name: Name of the frame
            x: X position
            y: Y position
            width: Frame width
            height: Frame height
            
        Returns:
            Dict[str, Any]: Frame creation result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-layout-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_frame",
                    arguments={
                        "file_id": file_id,
                        "page_id": page_id,
                        "name": name,
                        "x": x,
                        "y": y,
                        "width": width,
                        "height": height
                    }
                )
                
                logger.info(f"Created frame '{name}' in page {page_id}")
                return {
                    "success": True,
                    "frame_id": result.content[0].text if result.content else None,
                    "frame_name": name,
                    "dimensions": {"x": x, "y": y, "width": width, "height": height}
                }
                
        except Exception as e:
            logger.error(f"Failed to create frame {name}: {e}")
            raise TaskExecutionError(
                f"Failed to create frame: {str(e)}",
                task_type="create_frame",
                execution_stage="mcp_call"
            )
    
    async def setup_auto_layout(
        self,
        file_id: str,
        node_id: str,
        direction: str = "VERTICAL",
        spacing: float = 10,
        padding: Dict[str, float] = None
    ) -> Dict[str, Any]:
        """
        Set up auto-layout for a frame or component.
        
        Args:
            file_id: ID of the Figma file
            node_id: ID of the node to apply auto-layout to
            direction: Layout direction (VERTICAL or HORIZONTAL)
            spacing: Spacing between items
            padding: Padding values
            
        Returns:
            Dict[str, Any]: Auto-layout setup result
        """
        try:
            padding = padding or {"top": 20, "right": 20, "bottom": 20, "left": 20}
            
            async with mcp_client_manager.get_connection(
                "figma-layout-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "setup_auto_layout",
                    arguments={
                        "file_id": file_id,
                        "node_id": node_id,
                        "direction": direction,
                        "spacing": spacing,
                        "padding": padding
                    }
                )
                
                logger.info(f"Set up auto-layout for node {node_id}")
                return {
                    "success": True,
                    "node_id": node_id,
                    "direction": direction,
                    "spacing": spacing,
                    "padding": padding
                }
                
        except Exception as e:
            logger.error(f"Failed to setup auto-layout for node {node_id}: {e}")
            raise TaskExecutionError(
                f"Failed to setup auto-layout: {str(e)}",
                task_type="setup_auto_layout",
                execution_stage="mcp_call"
            )
    
    async def create_grid_system(
        self,
        file_id: str,
        node_id: str,
        grid_type: str = "COLUMNS",
        count: int = 12,
        gutter: float = 20,
        margin: float = 20
    ) -> Dict[str, Any]:
        """
        Create a grid system for a frame.
        
        Args:
            file_id: ID of the Figma file
            node_id: ID of the frame
            grid_type: Type of grid (COLUMNS, ROWS, or GRID)
            count: Number of columns/rows
            gutter: Gutter size
            margin: Margin size
            
        Returns:
            Dict[str, Any]: Grid system creation result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-layout-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_grid_system",
                    arguments={
                        "file_id": file_id,
                        "node_id": node_id,
                        "grid_type": grid_type,
                        "count": count,
                        "gutter": gutter,
                        "margin": margin
                    }
                )
                
                logger.info(f"Created {grid_type} grid system for node {node_id}")
                return {
                    "success": True,
                    "node_id": node_id,
                    "grid_type": grid_type,
                    "count": count,
                    "gutter": gutter,
                    "margin": margin
                }
                
        except Exception as e:
            logger.error(f"Failed to create grid system for node {node_id}: {e}")
            raise TaskExecutionError(
                f"Failed to create grid system: {str(e)}",
                task_type="create_grid_system",
                execution_stage="mcp_call"
            )
    
    async def set_constraints(
        self,
        file_id: str,
        node_id: str,
        horizontal: str = "LEFT",
        vertical: str = "TOP"
    ) -> Dict[str, Any]:
        """
        Set constraints for responsive behavior.
        
        Args:
            file_id: ID of the Figma file
            node_id: ID of the node
            horizontal: Horizontal constraint (LEFT, RIGHT, CENTER, LEFT_RIGHT, SCALE)
            vertical: Vertical constraint (TOP, BOTTOM, CENTER, TOP_BOTTOM, SCALE)
            
        Returns:
            Dict[str, Any]: Constraints setting result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-layout-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "set_constraints",
                    arguments={
                        "file_id": file_id,
                        "node_id": node_id,
                        "horizontal": horizontal,
                        "vertical": vertical
                    }
                )
                
                logger.info(f"Set constraints for node {node_id}")
                return {
                    "success": True,
                    "node_id": node_id,
                    "horizontal": horizontal,
                    "vertical": vertical
                }
                
        except Exception as e:
            logger.error(f"Failed to set constraints for node {node_id}: {e}")
            raise TaskExecutionError(
                f"Failed to set constraints: {str(e)}",
                task_type="set_constraints",
                execution_stage="mcp_call"
            )
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        """
        Execute layout management task.
        
        Args:
            task: Task to execute
            
        Returns:
            AgentResponse: Execution result
        """
        try:
            task_type = task.task_type.lower()
            parameters = task.parameters
            
            if task_type in ["create_frame", "add_frame", "frame"]:
                result = await self.create_frame(
                    file_id=parameters.get("file_id", ""),
                    page_id=parameters.get("page_id", ""),
                    name=parameters.get("name", "Frame"),
                    x=parameters.get("x", 0),
                    y=parameters.get("y", 0),
                    width=parameters.get("width", 375),
                    height=parameters.get("height", 812)
                )
            elif task_type in ["auto_layout", "autolayout", "layout"]:
                result = await self.setup_auto_layout(
                    file_id=parameters.get("file_id", ""),
                    node_id=parameters.get("node_id", ""),
                    direction=parameters.get("direction", "VERTICAL"),
                    spacing=parameters.get("spacing", 10),
                    padding=parameters.get("padding")
                )
            elif task_type in ["grid", "layout_grid", "column_grid"]:
                result = await self.create_grid_system(
                    file_id=parameters.get("file_id", ""),
                    node_id=parameters.get("node_id", ""),
                    grid_type=parameters.get("grid_type", "COLUMNS"),
                    count=parameters.get("count", 12),
                    gutter=parameters.get("gutter", 20),
                    margin=parameters.get("margin", 20)
                )
            elif task_type in ["constraints", "responsive", "resize"]:
                result = await self.set_constraints(
                    file_id=parameters.get("file_id", ""),
                    node_id=parameters.get("node_id", ""),
                    horizontal=parameters.get("horizontal", "LEFT"),
                    vertical=parameters.get("vertical", "TOP")
                )
            else:
                return AgentResponse(
                    success=False,
                    error_message=f"Unknown layout operation: {task_type}"
                )
            
            return AgentResponse(
                success=True,
                result=result,
                metadata={
                    "agent": self.name,
                    "task_type": task_type,
                    "parameters": parameters
                }
            )
            
        except Exception as e:
            logger.error(f"Frame layout agent execution failed: {e}")
            return AgentResponse(
                success=False,
                error_message=f"Layout operation failed: {str(e)}"
            )


# Register the frame layout agent
AgentFactory.register_agent(FrameLayoutAgent, "FrameLayoutAgent")
