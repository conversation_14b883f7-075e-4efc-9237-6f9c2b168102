#!/usr/bin/env python3
"""
Debug script to identify MCP connection issues.
"""

import asyncio
import httpx
import json
import subprocess
import sys
import os
from pathlib import Path

def test_command_availability():
    """Test if common MCP runtime commands are available."""
    print("🔍 TESTING COMMAND AVAILABILITY")
    print("=" * 50)
    
    commands = ["bun", "npx", "node", "python"]
    available_commands = {}
    
    for cmd in commands:
        try:
            result = subprocess.run([cmd, "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                available_commands[cmd] = result.stdout.strip()
                print(f"✅ {cmd}: {result.stdout.strip()}")
            else:
                available_commands[cmd] = None
                print(f"❌ {cmd}: Not available or error")
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError) as e:
            available_commands[cmd] = None
            print(f"❌ {cmd}: {str(e)}")
    
    return available_commands

def test_backend_connection():
    """Test backend API connectivity."""
    print("\n🌐 TESTING BACKEND CONNECTIVITY")
    print("=" * 50)
    
    try:
        client = httpx.Client(timeout=10.0)
        
        # Test health endpoint
        response = client.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ Backend health check: OK")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
            
        # Test MCP servers endpoint
        response = client.get("http://localhost:8000/mcp/servers")
        if response.status_code == 200:
            servers = response.json()
            print(f"✅ MCP servers endpoint: OK ({len(servers)} servers)")
        else:
            print(f"❌ MCP servers endpoint failed: {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Backend connection error: {e}")
        return False

def test_mcp_connection_with_simple_server():
    """Test MCP connection with a simple test configuration."""
    print("\n🔧 TESTING MCP CONNECTION")
    print("=" * 50)
    
    # Test configurations that should work
    test_configs = [
        {
            "name": "test-python-echo",
            "command": "python",
            "args": ["-c", "print('Hello from Python MCP test')"],
            "env": {},
            "description": "Simple Python test"
        }
    ]
    
    client = httpx.Client(timeout=30.0)
    
    for config in test_configs:
        print(f"\n📋 Testing: {config['name']}")
        print(f"   Command: {config['command']} {' '.join(config['args'])}")
        
        try:
            response = client.post(
                "http://localhost:8000/mcp/servers/connect",
                json={"server_config": config}
            )
            
            result = response.json()
            print(f"   Status Code: {response.status_code}")
            print(f"   Response: {json.dumps(result, indent=2)}")
            
            if response.status_code == 200 and result.get('success'):
                print("   ✅ Connection successful!")
            else:
                print("   ❌ Connection failed")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def check_environment_setup():
    """Check environment variables and configuration."""
    print("\n⚙️ CHECKING ENVIRONMENT SETUP")
    print("=" * 50)
    
    # Check for .env file
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file found")
    else:
        print("⚠️ .env file not found (using defaults)")
    
    # Check important environment variables
    important_vars = [
        "GEMINI_API_KEY",
        "FIGMA_ACCESS_TOKEN", 
        "API_PORT",
        "STREAMLIT_PORT"
    ]
    
    for var in important_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if "TOKEN" in var or "KEY" in var:
                masked_value = f"{value[:8]}..." if len(value) > 8 else "***"
                print(f"✅ {var}: {masked_value}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"⚠️ {var}: Not set")

def main():
    """Run all diagnostic tests."""
    print("🚀 MCP CONNECTION DIAGNOSTIC TOOL")
    print("=" * 60)
    
    # Test 1: Command availability
    available_commands = test_command_availability()
    
    # Test 2: Backend connectivity
    backend_ok = test_backend_connection()
    
    # Test 3: Environment setup
    check_environment_setup()
    
    # Test 4: MCP connection (only if backend is OK)
    if backend_ok:
        test_mcp_connection_with_simple_server()
    else:
        print("\n⚠️ Skipping MCP connection test due to backend issues")
    
    # Summary
    print("\n📋 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    working_commands = [cmd for cmd, version in available_commands.items() if version]
    print(f"✅ Available commands: {', '.join(working_commands) if working_commands else 'None'}")
    print(f"🌐 Backend connectivity: {'✅ OK' if backend_ok else '❌ Failed'}")
    
    if not working_commands:
        print("\n💡 RECOMMENDATION: Install at least one MCP runtime:")
        print("   - For Node.js/TypeScript: Install Node.js and npm")
        print("   - For Bun: Install Bun runtime")
        print("   - Python should already be available")
    
    if not backend_ok:
        print("\n💡 RECOMMENDATION: Check if backend is running:")
        print("   - Run: uv run python main.py")
        print("   - Check logs for any startup errors")

if __name__ == "__main__":
    main()
