#!/usr/bin/env python3
"""Test chat endpoint."""

import httpx
import json

def test_chat():
    try:
        client = httpx.Client(timeout=30.0)
        
        # Test chat endpoint
        response = client.post(
            "http://localhost:8000/chat",
            json={
                "message": "Hello, test without API keys",
                "use_mcp_tools": True
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat endpoint working!")
            print(f"Response: {result['response'][:200]}...")
            print(f"AI Features detected: {'AI Features' in result['response']}")
            print(f"Demo mode detected: {'demo mode' in result['response'].lower()}")
        else:
            print(f"❌ Chat endpoint error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_chat()
