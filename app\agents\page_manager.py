"""
Page Manager Agent for the Figma MCP client.

This agent handles page lifecycle management including create, rename, delete,
and reorder pages within Figma files through MCP tools.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
import json

from mcp import StdioServerParameters
from mcp.types import Too<PERSON>, TextContent

from .base import BaseAgent, AgentTask, AgentResponse, AgentFactory
from ..core.config import settings
from ..core.mcp_client import mcp_client_manager
from ..core.exceptions import Agent<PERSON><PERSON>r, TaskExecutionError


logger = logging.getLogger(__name__)


class PageManagerAgent(BaseAgent):
    """
    Specialist agent for Figma page management operations.
    
    Handles page creation, renaming, deletion, reordering, and navigation
    within Figma files through the Figma MCP server.
    """
    
    def __init__(self):
        super().__init__(
            name="PageManagerAgent",
            description="Manages Figma page operations including create, rename, delete, and reorder"
        )
        
        self.capabilities = [
            "page_creation",
            "page_renaming",
            "page_deletion",
            "page_reordering",
            "page_navigation",
            "page_duplication",
            "page_metadata"
        ]
        
        # MCP server parameters for Figma page operations
        self.figma_server_params = StdioServerParameters(
            command="figma-mcp-server",
            args=["--page-operations"],
            env={"FIGMA_ACCESS_TOKEN": settings.figma_access_token} if settings.figma_access_token else None
        )
    
    async def can_handle(self, task: str) -> bool:
        """Check if this agent can handle the given task."""
        page_operations = [
            "create_page", "add_page", "new_page",
            "rename_page", "delete_page", "remove_page",
            "reorder_pages", "move_page", "duplicate_page",
            "navigate_page", "switch_page", "list_pages",
            "get_page_info", "set_page_properties"
        ]
        
        return any(op in task.lower() for op in page_operations)
    
    async def get_available_tools(self) -> List[Tool]:
        """Get available MCP tools for page operations."""
        try:
            async with mcp_client_manager.get_connection(
                "figma-page-server", 
                self.figma_server_params
            ) as session:
                tools_response = await session.list_tools()
                return tools_response.tools
        except Exception as e:
            logger.error(f"Failed to get page management tools: {e}")
            return []
    
    async def create_page(self, file_id: str, name: str, template: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new page in a Figma file.
        
        Args:
            file_id: ID of the Figma file
            name: Name of the new page
            template: Optional page template
            
        Returns:
            Dict[str, Any]: Page creation result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-page-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_page",
                    arguments={
                        "file_id": file_id,
                        "name": name,
                        "template": template or "blank"
                    }
                )
                
                logger.info(f"Created page '{name}' in file {file_id}")
                return {
                    "success": True,
                    "page_id": result.content[0].text if result.content else None,
                    "page_name": name,
                    "file_id": file_id
                }
                
        except Exception as e:
            logger.error(f"Failed to create page {name} in file {file_id}: {e}")
            raise TaskExecutionError(
                f"Failed to create page: {str(e)}",
                task_type="create_page",
                execution_stage="mcp_call"
            )
    
    async def rename_page(self, file_id: str, page_id: str, new_name: str) -> Dict[str, Any]:
        """
        Rename an existing page.
        
        Args:
            file_id: ID of the Figma file
            page_id: ID of the page to rename
            new_name: New name for the page
            
        Returns:
            Dict[str, Any]: Page renaming result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-page-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "rename_page",
                    arguments={
                        "file_id": file_id,
                        "page_id": page_id,
                        "new_name": new_name
                    }
                )
                
                logger.info(f"Renamed page {page_id} to '{new_name}' in file {file_id}")
                return {
                    "success": True,
                    "page_id": page_id,
                    "new_name": new_name,
                    "file_id": file_id
                }
                
        except Exception as e:
            logger.error(f"Failed to rename page {page_id} in file {file_id}: {e}")
            raise TaskExecutionError(
                f"Failed to rename page: {str(e)}",
                task_type="rename_page",
                execution_stage="mcp_call"
            )
    
    async def delete_page(self, file_id: str, page_id: str) -> Dict[str, Any]:
        """
        Delete a page from a Figma file.
        
        Args:
            file_id: ID of the Figma file
            page_id: ID of the page to delete
            
        Returns:
            Dict[str, Any]: Page deletion result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-page-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "delete_page",
                    arguments={
                        "file_id": file_id,
                        "page_id": page_id
                    }
                )
                
                logger.info(f"Deleted page {page_id} from file {file_id}")
                return {
                    "success": True,
                    "page_id": page_id,
                    "file_id": file_id,
                    "message": "Page deleted successfully"
                }
                
        except Exception as e:
            logger.error(f"Failed to delete page {page_id} from file {file_id}: {e}")
            raise TaskExecutionError(
                f"Failed to delete page: {str(e)}",
                task_type="delete_page",
                execution_stage="mcp_call"
            )
    
    async def reorder_pages(self, file_id: str, page_order: List[str]) -> Dict[str, Any]:
        """
        Reorder pages in a Figma file.
        
        Args:
            file_id: ID of the Figma file
            page_order: List of page IDs in desired order
            
        Returns:
            Dict[str, Any]: Page reordering result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-page-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "reorder_pages",
                    arguments={
                        "file_id": file_id,
                        "page_order": page_order
                    }
                )
                
                logger.info(f"Reordered pages in file {file_id}")
                return {
                    "success": True,
                    "file_id": file_id,
                    "new_order": page_order,
                    "message": "Pages reordered successfully"
                }
                
        except Exception as e:
            logger.error(f"Failed to reorder pages in file {file_id}: {e}")
            raise TaskExecutionError(
                f"Failed to reorder pages: {str(e)}",
                task_type="reorder_pages",
                execution_stage="mcp_call"
            )
    
    async def list_pages(self, file_id: str) -> Dict[str, Any]:
        """
        List all pages in a Figma file.
        
        Args:
            file_id: ID of the Figma file
            
        Returns:
            Dict[str, Any]: List of pages
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-page-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "list_pages",
                    arguments={"file_id": file_id}
                )
                
                logger.info(f"Listed pages for file {file_id}")
                return {
                    "success": True,
                    "file_id": file_id,
                    "pages": json.loads(result.content[0].text) if result.content else []
                }
                
        except Exception as e:
            logger.error(f"Failed to list pages for file {file_id}: {e}")
            raise TaskExecutionError(
                f"Failed to list pages: {str(e)}",
                task_type="list_pages",
                execution_stage="mcp_call"
            )
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        """
        Execute page management task.
        
        Args:
            task: Task to execute
            
        Returns:
            AgentResponse: Execution result
        """
        try:
            task_type = task.task_type.lower()
            parameters = task.parameters
            
            if task_type in ["create_page", "add_page", "new_page"]:
                result = await self.create_page(
                    file_id=parameters.get("file_id", ""),
                    name=parameters.get("name", "New Page"),
                    template=parameters.get("template")
                )
            elif task_type == "rename_page":
                result = await self.rename_page(
                    file_id=parameters.get("file_id", ""),
                    page_id=parameters.get("page_id", ""),
                    new_name=parameters.get("new_name", "")
                )
            elif task_type in ["delete_page", "remove_page"]:
                result = await self.delete_page(
                    file_id=parameters.get("file_id", ""),
                    page_id=parameters.get("page_id", "")
                )
            elif task_type in ["reorder_pages", "move_page"]:
                result = await self.reorder_pages(
                    file_id=parameters.get("file_id", ""),
                    page_order=parameters.get("page_order", [])
                )
            elif task_type == "list_pages":
                result = await self.list_pages(
                    file_id=parameters.get("file_id", "")
                )
            else:
                return AgentResponse(
                    success=False,
                    error_message=f"Unknown page operation: {task_type}"
                )
            
            return AgentResponse(
                success=True,
                result=result,
                metadata={
                    "agent": self.name,
                    "task_type": task_type,
                    "parameters": parameters
                }
            )
            
        except Exception as e:
            logger.error(f"Page manager agent execution failed: {e}")
            return AgentResponse(
                success=False,
                error_message=f"Page operation failed: {str(e)}"
            )


# Register the page manager agent
AgentFactory.register_agent(PageManagerAgent, "PageManagerAgent")
