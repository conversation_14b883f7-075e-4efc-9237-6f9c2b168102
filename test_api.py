#!/usr/bin/env python3
"""
Simple test script to verify the Figma MCP Multi-Agent Client API is working.
"""

import httpx
import json

def test_api():
    """Test the API with a simple query."""
    
    # Test health endpoint first
    print("Testing health endpoint...")
    try:
        response = httpx.get("http://localhost:8000/health")
        print(f"Health check: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"Health check failed: {e}")
        return
    
    # Test task execution
    print("\nTesting task execution...")
    try:
        payload = {
            "query": "Create a new Figma file called 'Test Project'",
            "context": {},
            "timeout": 60.0
        }
        
        response = httpx.post(
            "http://localhost:8000/tasks/execute",
            json=payload,
            timeout=30.0
        )
        
        print(f"Task execution: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        
    except Exception as e:
        print(f"Task execution failed: {e}")
    
    # Test agent status
    print("\nTesting agent status...")
    try:
        response = httpx.get("http://localhost:8000/agents/status")
        print(f"Agent status: {response.status_code}")
        result = response.json()
        print(f"Total agents: {result.get('total_agents', 0)}")
        print(f"Active agents: {result.get('active_agents', 0)}")
        print(f"Busy agents: {result.get('busy_agents', 0)}")
        
    except Exception as e:
        print(f"Agent status failed: {e}")

if __name__ == "__main__":
    test_api()
