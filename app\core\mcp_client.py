"""
MCP Client Manager for the Figma MCP client.

This module provides connection management, connection pooling, and
circuit breaker patterns for MCP server communication.
"""

import asyncio
import logging
from typing import Dict, Optional, List, Any, AsyncContextManager, AsyncGenerator
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum
import time

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from .config import settings
from .exceptions import (
    MCPConnectionError,
    MCPTimeoutError,
    CircuitBreakerError,
    FigmaMCPError
)


logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration."""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    expected_exception: type = Exception


@dataclass
class CircuitBreaker:
    """Circuit breaker implementation for MCP connections."""
    config: CircuitBreakerConfig
    failure_count: int = 0
    last_failure_time: Optional[float] = None
    state: CircuitBreakerState = CircuitBreakerState.CLOSED
    
    def can_execute(self) -> bool:
        """Check if execution is allowed."""
        if self.state == CircuitBreakerState.CLOSED:
            return True
        elif self.state == CircuitBreakerState.OPEN:
            if (time.time() - (self.last_failure_time or 0)) > self.config.recovery_timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Record successful execution."""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
    
    def record_failure(self):
        """Record failed execution."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitBreakerState.OPEN


@dataclass
class MCPConnection:
    """MCP connection wrapper with metadata."""
    session: ClientSession
    server_params: StdioServerParameters
    created_at: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)
    is_healthy: bool = True
    connection_id: str = ""


class MCPClientManager:
    """
    MCP Client Manager with connection pooling and circuit breaker patterns.
    
    Manages multiple MCP server connections with automatic failover,
    connection pooling, and circuit breaker protection.
    """
    
    def __init__(self):
        self.connections: Dict[str, MCPConnection] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.connection_semaphore = asyncio.Semaphore(settings.mcp_max_connections)
        self._lock = asyncio.Lock()
    
    def _get_circuit_breaker(self, server_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for server."""
        if server_name not in self.circuit_breakers:
            config = CircuitBreakerConfig(
                failure_threshold=3,
                recovery_timeout=30.0,
                expected_exception=MCPConnectionError
            )
            self.circuit_breakers[server_name] = CircuitBreaker(config)
        return self.circuit_breakers[server_name]
    
    async def _create_connection(
        self,
        server_name: str,
        server_params: StdioServerParameters
    ) -> MCPConnection:
        """Create a new MCP connection."""
        try:
            logger.info(f"Attempting to connect to MCP server {server_name} with command: {server_params.command} {' '.join(server_params.args)}")

            # Use stdio client to connect to MCP server
            stdio_context = stdio_client(server_params)
            read_stream, write_stream = await stdio_context.__aenter__()

            # Create client session
            session = ClientSession(read_stream, write_stream)
            await session.__aenter__()

            # Initialize the connection with timeout
            try:
                await asyncio.wait_for(session.initialize(), timeout=settings.mcp_connection_timeout)
            except asyncio.TimeoutError:
                logger.error(f"MCP server {server_name} initialization timed out")
                raise MCPConnectionError(
                    f"MCP server {server_name} initialization timed out. The process may not be a valid MCP server.",
                    server_url=f"{server_params.command}",
                    connection_type="stdio"
                )

            connection = MCPConnection(
                session=session,
                server_params=server_params,
                connection_id=f"{server_name}_{int(time.time())}"
            )

            logger.info(f"Successfully created MCP connection to {server_name}")
            return connection

        except MCPConnectionError:
            # Re-raise MCP-specific errors
            raise
        except FileNotFoundError as e:
            logger.error(f"Command not found for MCP server {server_name}: {e}")
            raise MCPConnectionError(
                f"Command '{server_params.command}' not found. Make sure it's installed and in your PATH.",
                server_url=f"{server_params.command}",
                connection_type="stdio"
            ) from e
        except PermissionError as e:
            logger.error(f"Permission denied for MCP server {server_name}: {e}")
            raise MCPConnectionError(
                f"Permission denied when executing '{server_params.command}'. Check file permissions.",
                server_url=f"{server_params.command}",
                connection_type="stdio"
            ) from e
        except Exception as e:
            logger.error(f"Failed to create MCP connection to {server_name}: {e}")
            error_msg = str(e)

            # Provide more specific error messages based on the error type
            if "Connection refused" in error_msg:
                detailed_msg = f"Connection refused. The command '{server_params.command}' may not be running an MCP server."
            elif "No such file or directory" in error_msg:
                detailed_msg = f"File not found: '{server_params.command}'. Check the command path."
            elif "not a valid MCP server" in error_msg.lower():
                detailed_msg = f"The process is not responding as a valid MCP server. Ensure '{server_params.command}' implements the MCP protocol."
            else:
                detailed_msg = f"Failed to connect to MCP server {server_name}: {error_msg}"

            raise MCPConnectionError(
                detailed_msg,
                server_url=f"{server_params.command}",
                connection_type="stdio"
            ) from e
    
    @asynccontextmanager
    async def get_connection(
        self,
        server_name: str,
        server_params: StdioServerParameters
    ) -> AsyncContextManager[ClientSession]:
        """
        Get an MCP connection with circuit breaker protection.
        
        Args:
            server_name: Name of the MCP server
            server_params: Server connection parameters
            
        Yields:
            ClientSession: Active MCP client session
            
        Raises:
            CircuitBreakerError: If circuit breaker is open
            MCPConnectionError: If connection fails
            MCPTimeoutError: If connection times out
        """
        circuit_breaker = self._get_circuit_breaker(server_name)
        
        if not circuit_breaker.can_execute():
            raise CircuitBreakerError(
                f"Circuit breaker is open for {server_name}",
                service_name=server_name,
                failure_count=circuit_breaker.failure_count
            )
        
        async with self.connection_semaphore:
            try:
                async with self._lock:
                    # Check if we have an existing healthy connection
                    if (server_name in self.connections and 
                        self.connections[server_name].is_healthy):
                        connection = self.connections[server_name]
                        connection.last_used = time.time()
                    else:
                        # Create new connection
                        connection = await self._create_connection(server_name, server_params)
                        self.connections[server_name] = connection
                
                # Yield the session
                yield connection.session
                
                # Record success
                circuit_breaker.record_success()
                
            except Exception as e:
                # Record failure
                circuit_breaker.record_failure()
                
                # Mark connection as unhealthy
                if server_name in self.connections:
                    self.connections[server_name].is_healthy = False
                
                # Re-raise appropriate exception
                if isinstance(e, asyncio.TimeoutError):
                    raise MCPTimeoutError(
                        f"MCP connection to {server_name} timed out",
                        timeout_duration=settings.mcp_connection_timeout
                    ) from e
                elif isinstance(e, FigmaMCPError):
                    raise
                else:
                    raise MCPConnectionError(
                        f"MCP connection error for {server_name}: {str(e)}",
                        server_url=f"{server_params.command}"
                    ) from e
    
    async def health_check(self, server_name: str) -> bool:
        """
        Perform health check on MCP connection.
        
        Args:
            server_name: Name of the MCP server
            
        Returns:
            bool: True if connection is healthy
        """
        try:
            if server_name not in self.connections:
                return False
            
            connection = self.connections[server_name]
            
            # Simple ping by listing tools (lightweight operation)
            await asyncio.wait_for(
                connection.session.list_tools(),
                timeout=5.0
            )
            
            connection.is_healthy = True
            return True
            
        except Exception as e:
            logger.warning(f"Health check failed for {server_name}: {e}")
            if server_name in self.connections:
                self.connections[server_name].is_healthy = False
            return False
    
    async def close_connection(self, server_name: str):
        """Close MCP connection."""
        if server_name in self.connections:
            try:
                connection = self.connections[server_name]
                await connection.session.__aexit__(None, None, None)
                del self.connections[server_name]
                logger.info(f"Closed MCP connection to {server_name}")
            except Exception as e:
                logger.error(f"Error closing connection to {server_name}: {e}")
    
    async def close_all_connections(self):
        """Close all MCP connections."""
        for server_name in list(self.connections.keys()):
            await self.close_connection(server_name)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        stats = {
            "total_connections": len(self.connections),
            "healthy_connections": sum(
                1 for conn in self.connections.values() if conn.is_healthy
            ),
            "circuit_breaker_states": {
                name: breaker.state.value
                for name, breaker in self.circuit_breakers.items()
            }
        }
        return stats

    def list_connected_servers(self) -> List[str]:
        """List all connected server names."""
        return list(self.connections.keys())

    def is_server_connected(self, server_name: str) -> bool:
        """Check if a server is connected."""
        return (server_name in self.connections and
                self.connections[server_name].session is not None)

    def is_server_healthy(self, server_name: str) -> bool:
        """Check if a server connection is healthy."""
        return (server_name in self.connections and
                self.connections[server_name].is_healthy)


# Global MCP client manager instance
mcp_client_manager = MCPClientManager()
