# Figma MCP Client Configuration

# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyDxBNTBKCaTPeWC39CxTKI_N_bdQu-SdG4
GEMINI_MODEL=gemini-2.0-flash

# Figma API Configuration (for direct API integration)
FIGMA_ACCESS_TOKEN=*********************************************

# MCP Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=3001
MCP_CONNECTION_TIMEOUT=30
MCP_MAX_CONNECTIONS=10

# Redis Configuration (for task queuing)
REDIS_URL=redis://localhost:6379/0

# FastAPI Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true

# Streamlit Configuration
STREAMLIT_HOST=localhost
STREAMLIT_PORT=8501

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security Configuration
SECRET_KEY=your_secret_key_here
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8501"]

# Performance Configuration
MAX_CONCURRENT_AGENTS=5
AGENT_TIMEOUT=60
TASK_QUEUE_SIZE=100
