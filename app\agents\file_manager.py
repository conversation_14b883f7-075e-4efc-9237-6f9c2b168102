"""
File Manager Agent for the Figma MCP client.

This agent handles file operations including create, open, delete, and organize
Figma files through MCP tools.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
import json

from mcp import StdioServerParameters
from mcp.types import <PERSON><PERSON>, TextContent

from .base import BaseAgent, AgentTask, AgentResponse, AgentFactory
from ..core.config import settings
from ..core.mcp_client import mcp_client_manager
from ..core.exceptions import Agent<PERSON><PERSON>r, TaskExecutionError


logger = logging.getLogger(__name__)


class FileManagerAgent(BaseAgent):
    """
    Specialist agent for Figma file management operations.
    
    Handles file creation, opening, deletion, organization, and metadata management
    through the Figma MCP server.
    """
    
    def __init__(self):
        super().__init__(
            name="FileManagerAgent",
            description="Manages Figma file operations including create, open, delete, and organize"
        )
        
        self.capabilities = [
            "file_creation",
            "file_opening",
            "file_deletion",
            "file_organization",
            "file_metadata",
            "file_search",
            "file_sharing"
        ]
        
        # MCP server parameters for Figma
        self.figma_server_params = StdioServerParameters(
            command="figma-mcp-server",  # This would be the actual Figma MCP server
            args=["--file-operations"],
            env={"FIGMA_ACCESS_TOKEN": settings.figma_access_token} if settings.figma_access_token else None
        )
    
    async def can_handle(self, task: str) -> bool:
        """Check if this agent can handle the given task."""
        file_operations = [
            "create_file", "open_file", "delete_file", "rename_file",
            "duplicate_file", "organize_files", "search_files",
            "get_file_info", "share_file", "unshare_file",
            "move_file", "archive_file", "restore_file"
        ]
        
        return any(op in task.lower() for op in file_operations)
    
    async def get_available_tools(self) -> List[Tool]:
        """Get available MCP tools for file operations."""
        try:
            async with mcp_client_manager.get_connection(
                "figma-file-server", 
                self.figma_server_params
            ) as session:
                tools_response = await session.list_tools()
                return tools_response.tools
        except Exception as e:
            logger.error(f"Failed to get file management tools: {e}")
            return []
    
    async def create_file(self, name: str, template: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new Figma file.
        
        Args:
            name: Name of the new file
            template: Optional template to use
            
        Returns:
            Dict[str, Any]: File creation result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-file-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_file",
                    arguments={
                        "name": name,
                        "template": template or "blank"
                    }
                )
                
                logger.info(f"Created Figma file: {name}")
                return {
                    "success": True,
                    "file_id": result.content[0].text if result.content else None,
                    "file_name": name,
                    "template": template
                }
                
        except Exception as e:
            logger.error(f"Failed to create file {name}: {e}")
            raise TaskExecutionError(
                f"Failed to create file: {str(e)}",
                task_type="create_file",
                execution_stage="mcp_call"
            )
    
    async def open_file(self, file_id: str) -> Dict[str, Any]:
        """
        Open an existing Figma file.
        
        Args:
            file_id: ID of the file to open
            
        Returns:
            Dict[str, Any]: File opening result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-file-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "open_file",
                    arguments={"file_id": file_id}
                )
                
                logger.info(f"Opened Figma file: {file_id}")
                return {
                    "success": True,
                    "file_id": file_id,
                    "file_data": result.content[0].text if result.content else None
                }
                
        except Exception as e:
            logger.error(f"Failed to open file {file_id}: {e}")
            raise TaskExecutionError(
                f"Failed to open file: {str(e)}",
                task_type="open_file",
                execution_stage="mcp_call"
            )
    
    async def delete_file(self, file_id: str) -> Dict[str, Any]:
        """
        Delete a Figma file.
        
        Args:
            file_id: ID of the file to delete
            
        Returns:
            Dict[str, Any]: File deletion result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-file-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "delete_file",
                    arguments={"file_id": file_id}
                )
                
                logger.info(f"Deleted Figma file: {file_id}")
                return {
                    "success": True,
                    "file_id": file_id,
                    "message": "File deleted successfully"
                }
                
        except Exception as e:
            logger.error(f"Failed to delete file {file_id}: {e}")
            raise TaskExecutionError(
                f"Failed to delete file: {str(e)}",
                task_type="delete_file",
                execution_stage="mcp_call"
            )
    
    async def search_files(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search for Figma files.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            Dict[str, Any]: Search results
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-file-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "search_files",
                    arguments={
                        "query": query,
                        "limit": limit
                    }
                )
                
                logger.info(f"Searched files with query: {query}")
                return {
                    "success": True,
                    "query": query,
                    "results": json.loads(result.content[0].text) if result.content else []
                }
                
        except Exception as e:
            logger.error(f"Failed to search files with query {query}: {e}")
            raise TaskExecutionError(
                f"Failed to search files: {str(e)}",
                task_type="search_files",
                execution_stage="mcp_call"
            )
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        """
        Execute file management task.
        
        Args:
            task: Task to execute
            
        Returns:
            AgentResponse: Execution result
        """
        try:
            task_type = task.task_type.lower()
            parameters = task.parameters
            
            if task_type == "create_file":
                result = await self.create_file(
                    name=parameters.get("name", "Untitled"),
                    template=parameters.get("template")
                )
            elif task_type == "open_file":
                result = await self.open_file(
                    file_id=parameters.get("file_id", "")
                )
            elif task_type == "delete_file":
                result = await self.delete_file(
                    file_id=parameters.get("file_id", "")
                )
            elif task_type == "search_files":
                result = await self.search_files(
                    query=parameters.get("query", ""),
                    limit=parameters.get("limit", 10)
                )
            else:
                return AgentResponse(
                    success=False,
                    error_message=f"Unknown file operation: {task_type}"
                )
            
            return AgentResponse(
                success=True,
                result=result,
                metadata={
                    "agent": self.name,
                    "task_type": task_type,
                    "parameters": parameters
                }
            )
            
        except Exception as e:
            logger.error(f"File manager agent execution failed: {e}")
            return AgentResponse(
                success=False,
                error_message=f"File operation failed: {str(e)}"
            )


# Register the file manager agent
AgentFactory.register_agent(FileManagerAgent, "FileManagerAgent")
