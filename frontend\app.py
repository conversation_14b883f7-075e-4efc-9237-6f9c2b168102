"""
Streamlit frontend for the Figma MCP Multi-Agent Client.

This module provides a web interface with multi-agent dashboard, chat interface,
progress tracking, and real-time updates.
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add parent directory to Python path for imports
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

import streamlit as st
import httpx
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

# Now we can import from the app package
from app.core.config import settings


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Figma MCP Multi-Agent Client",
    page_icon="🎨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# API client configuration
API_BASE_URL = f"http://{settings.api_host}:{settings.api_port}"


class APIClient:
    """HTTP client for communicating with the FastAPI backend."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.client = httpx.Client(timeout=30.0)
    
    def execute_task(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a task through the API."""
        try:
            response = self.client.post(
                f"{self.base_url}/tasks/execute",
                json={
                    "query": query,
                    "context": context or {},
                    "timeout": 60.0
                }
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"API call failed: {e}")
            return {"success": False, "error_message": str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status from the API."""
        try:
            response = self.client.get(f"{self.base_url}/agents/status")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            return {}
    
    def get_agent_history(self, agent_name: str, limit: int = 10) -> Dict[str, Any]:
        """Get agent task history."""
        try:
            response = self.client.get(
                f"{self.base_url}/agents/{agent_name}/history",
                params={"limit": limit}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get agent history: {e}")
            return {"history": []}


# Initialize API client
@st.cache_resource
def get_api_client():
    return APIClient(API_BASE_URL)


# Initialize session state
def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "task_history" not in st.session_state:
        st.session_state.task_history = []
    if "selected_agent" not in st.session_state:
        st.session_state.selected_agent = None


def render_sidebar():
    """Render the sidebar with navigation and system status."""
    st.sidebar.title("🎨 Figma MCP Client")
    
    # Navigation
    page = st.sidebar.selectbox(
        "Navigate",
        ["Chat Interface", "Agent Dashboard", "System Status", "Task History"]
    )
    
    # System status summary
    st.sidebar.subheader("System Status")
    api_client = get_api_client()
    
    try:
        status = api_client.get_system_status()
        if status:
            st.sidebar.metric("Active Agents", status.get("active_agents", 0))
            st.sidebar.metric("Busy Agents", status.get("busy_agents", 0))
            st.sidebar.metric("Total Tasks", len(st.session_state.task_history))
        else:
            st.sidebar.error("Unable to connect to API")
    except Exception as e:
        st.sidebar.error(f"API Error: {str(e)}")
    
    return page


def render_chat_interface():
    """Render the main chat interface."""
    st.title("💬 Figma MCP Chat Interface")
    st.write("Ask me anything about Figma operations!")
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.write(message["content"])
            if message.get("metadata"):
                with st.expander("Details"):
                    st.json(message["metadata"])
    
    # Chat input
    if prompt := st.chat_input("What would you like to do in Figma?"):
        # Add user message
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        with st.chat_message("user"):
            st.write(prompt)
        
        # Process with API
        with st.chat_message("assistant"):
            with st.spinner("Processing your request..."):
                api_client = get_api_client()
                result = api_client.execute_task(prompt)
                
                if result.get("success"):
                    st.success("Task completed successfully!")
                    if result.get("result"):
                        st.json(result["result"])
                    
                    # Add to task history
                    st.session_state.task_history.append({
                        "timestamp": datetime.now(),
                        "query": prompt,
                        "result": result,
                        "agent": result.get("agent_used", "Unknown")
                    })
                    
                    response_content = f"✅ Task completed successfully!\n\nAgent: {result.get('agent_used', 'Unknown')}\nExecution time: {result.get('execution_time', 0):.2f}s"
                    
                else:
                    st.error("Task failed!")
                    st.error(result.get("error_message", "Unknown error"))
                    response_content = f"❌ Task failed: {result.get('error_message', 'Unknown error')}"
                
                # Add assistant response
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": response_content,
                    "metadata": result
                })


def render_agent_dashboard():
    """Render the agent dashboard with status and metrics."""
    st.title("🤖 Agent Dashboard")
    
    api_client = get_api_client()
    status = api_client.get_system_status()
    
    if not status:
        st.error("Unable to load agent status")
        return
    
    # Overview metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Agents", status.get("total_agents", 0))
    with col2:
        st.metric("Active Agents", status.get("active_agents", 0))
    with col3:
        st.metric("Busy Agents", status.get("busy_agents", 0))
    with col4:
        st.metric("MCP Connections", status.get("mcp_connections", {}).get("total_connections", 0))
    
    # Agent details
    st.subheader("Agent Details")
    
    agents = status.get("agents", {})
    if agents:
        # Create agent status dataframe
        agent_data = []
        for name, agent_info in agents.items():
            agent_data.append({
                "Agent": name,
                "Status": "Busy" if agent_info.get("is_busy") else "Available",
                "Total Tasks": agent_info.get("total_tasks", 0),
                "Success Rate": f"{(agent_info.get('successful_tasks', 0) / max(agent_info.get('total_tasks', 1), 1) * 100):.1f}%",
                "Capabilities": len(agent_info.get("capabilities", []))
            })
        
        df = pd.DataFrame(agent_data)
        st.dataframe(df, use_container_width=True)
        
        # Agent selection for detailed view
        selected_agent = st.selectbox("Select agent for details:", list(agents.keys()))
        
        if selected_agent:
            agent_info = agents[selected_agent]
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader(f"{selected_agent} Details")
                st.write(f"**Description:** {agent_info.get('description', 'N/A')}")
                st.write(f"**Status:** {'Busy' if agent_info.get('is_busy') else 'Available'}")
                st.write(f"**Current Task:** {agent_info.get('current_task', 'None')}")
                
                st.write("**Capabilities:**")
                for capability in agent_info.get("capabilities", []):
                    st.write(f"• {capability}")
            
            with col2:
                # Task history for selected agent
                history = api_client.get_agent_history(selected_agent)
                if history.get("history"):
                    st.subheader("Recent Tasks")
                    for task in history["history"][:5]:
                        status_icon = "✅" if task.get("status") == "completed" else "❌"
                        st.write(f"{status_icon} {task.get('task_type', 'Unknown')} - {task.get('duration', 0):.2f}s")


def render_system_status():
    """Render system status and monitoring."""
    st.title("📊 System Status")
    
    api_client = get_api_client()
    status = api_client.get_system_status()
    
    if not status:
        st.error("Unable to load system status")
        return
    
    # System health indicators
    st.subheader("System Health")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Agent health
        total_agents = status.get("total_agents", 0)
        active_agents = status.get("active_agents", 0)
        health_ratio = active_agents / max(total_agents, 1)
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=health_ratio * 100,
            title={"text": "Agent Health %"},
            gauge={"axis": {"range": [None, 100]},
                   "bar": {"color": "darkgreen" if health_ratio > 0.8 else "orange" if health_ratio > 0.5 else "red"}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # MCP connections
        mcp_stats = status.get("mcp_connections", {})
        total_connections = mcp_stats.get("total_connections", 0)
        healthy_connections = mcp_stats.get("healthy_connections", 0)
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=healthy_connections / max(total_connections, 1) * 100,
            title={"text": "MCP Health %"},
            gauge={"axis": {"range": [None, 100]}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col3:
        # Task success rate
        if st.session_state.task_history:
            successful_tasks = sum(1 for task in st.session_state.task_history if task["result"].get("success"))
            success_rate = successful_tasks / len(st.session_state.task_history) * 100
        else:
            success_rate = 0
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=success_rate,
            title={"text": "Success Rate %"},
            gauge={"axis": {"range": [None, 100]}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)


def render_task_history():
    """Render task history and analytics."""
    st.title("📈 Task History")
    
    if not st.session_state.task_history:
        st.info("No tasks executed yet. Try the chat interface!")
        return
    
    # Task history table
    st.subheader("Recent Tasks")
    
    history_data = []
    for task in st.session_state.task_history[-20:]:  # Last 20 tasks
        history_data.append({
            "Timestamp": task["timestamp"].strftime("%Y-%m-%d %H:%M:%S"),
            "Query": task["query"][:50] + "..." if len(task["query"]) > 50 else task["query"],
            "Agent": task["agent"],
            "Status": "✅ Success" if task["result"].get("success") else "❌ Failed",
            "Duration": f"{task['result'].get('execution_time', 0):.2f}s"
        })
    
    df = pd.DataFrame(history_data)
    st.dataframe(df, use_container_width=True)
    
    # Analytics
    if len(st.session_state.task_history) > 1:
        st.subheader("Analytics")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Tasks by agent
            agent_counts = {}
            for task in st.session_state.task_history:
                agent = task["agent"]
                agent_counts[agent] = agent_counts.get(agent, 0) + 1
            
            fig = px.pie(
                values=list(agent_counts.values()),
                names=list(agent_counts.keys()),
                title="Tasks by Agent"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Success rate over time
            success_data = []
            for i, task in enumerate(st.session_state.task_history):
                success_data.append({
                    "Task": i + 1,
                    "Success": 1 if task["result"].get("success") else 0
                })
            
            df_success = pd.DataFrame(success_data)
            df_success["Rolling Success Rate"] = df_success["Success"].rolling(window=5, min_periods=1).mean() * 100
            
            fig = px.line(
                df_success,
                x="Task",
                y="Rolling Success Rate",
                title="Success Rate Trend (5-task rolling average)"
            )
            st.plotly_chart(fig, use_container_width=True)


def main():
    """Main Streamlit application."""
    initialize_session_state()
    
    # Render sidebar and get selected page
    page = render_sidebar()
    
    # Render selected page
    if page == "Chat Interface":
        render_chat_interface()
    elif page == "Agent Dashboard":
        render_agent_dashboard()
    elif page == "System Status":
        render_system_status()
    elif page == "Task History":
        render_task_history()


if __name__ == "__main__":
    main()
