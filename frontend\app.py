"""
Streamlit frontend for the Figma MCP Multi-Agent Client.

This module provides a web interface with multi-agent dashboard, chat interface,
progress tracking, and real-time updates.
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add parent directory to Python path for imports
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

import streamlit as st
import httpx
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

# Now we can import from the app package
from app.core.config import settings


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Figma MCP Multi-Agent Client",
    page_icon="🎨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if "conversation_history" not in st.session_state:
    st.session_state.conversation_history = []
if "conversation_id" not in st.session_state:
    st.session_state.conversation_id = None
if "mcp_servers" not in st.session_state:
    st.session_state.mcp_servers = []
if "available_tools" not in st.session_state:
    st.session_state.available_tools = {}

# API client configuration
API_BASE_URL = f"http://{settings.api_host}:{settings.api_port}"


class APIClient:
    """HTTP client for communicating with the FastAPI backend."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.client = httpx.Client(timeout=30.0)
    
    def execute_task(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a task through the API."""
        try:
            response = self.client.post(
                f"{self.base_url}/tasks/execute",
                json={
                    "query": query,
                    "context": context or {},
                    "timeout": 60.0
                }
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"API call failed: {e}")
            return {"success": False, "error_message": str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status from the API."""
        try:
            response = self.client.get(f"{self.base_url}/agents/status")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            return {}
    
    def get_agent_history(self, agent_name: str, limit: int = 10) -> Dict[str, Any]:
        """Get agent task history."""
        try:
            response = self.client.get(
                f"{self.base_url}/agents/{agent_name}/history",
                params={"limit": limit}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get agent history: {e}")
            return {"history": []}

    def get_mcp_servers(self) -> List[Dict[str, Any]]:
        """Get list of MCP servers."""
        try:
            response = self.client.get(f"{self.base_url}/mcp/servers")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get MCP servers: {e}")
            return []

    def connect_mcp_server(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """Connect to an MCP server."""
        try:
            response = self.client.post(
                f"{self.base_url}/mcp/servers/connect",
                json={"server_config": server_config}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to connect MCP server: {e}")
            return {"success": False, "message": str(e)}

    def disconnect_mcp_server(self, server_name: str) -> Dict[str, Any]:
        """Disconnect from an MCP server."""
        try:
            response = self.client.delete(f"{self.base_url}/mcp/servers/{server_name}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to disconnect MCP server: {e}")
            return {"success": False, "message": str(e)}

    def get_available_tools(self) -> Dict[str, Any]:
        """Get available MCP tools."""
        try:
            response = self.client.get(f"{self.base_url}/chat/tools")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get available tools: {e}")
            return {"tools_by_server": {}}

    def send_chat_message(self, message: str, conversation_id: Optional[str] = None,
                         use_mcp_tools: bool = True) -> Dict[str, Any]:
        """Send a chat message."""
        try:
            payload = {
                "message": message,
                "conversation_id": conversation_id,
                "use_mcp_tools": use_mcp_tools
            }
            response = self.client.post(f"{self.base_url}/chat", json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to send chat message: {e}")
            return {
                "response": f"Connection error: {str(e)}",
                "conversation_id": conversation_id or "error",
                "tool_calls": [],
                "tool_results": [],
                "available_tools": []
            }


# Initialize API client
@st.cache_resource
def get_api_client():
    return APIClient(API_BASE_URL)


# Initialize session state
def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "task_history" not in st.session_state:
        st.session_state.task_history = []
    if "selected_agent" not in st.session_state:
        st.session_state.selected_agent = None


def render_sidebar():
    """Render simplified sidebar with navigation and MCP controls."""
    st.sidebar.title("🎨 Figma MCP Client")

    # Navigation - Simplified to core functionality
    page = st.sidebar.selectbox(
        "Navigate",
        ["Chat Interface", "MCP Servers"]
    )

    st.sidebar.divider()

    # Prominent New MCP Server button
    if st.sidebar.button("➕ New MCP Server", type="primary", use_container_width=True):
        st.session_state.show_add_server = True
        # Switch to MCP Servers page
        st.session_state.selected_page = "MCP Servers"
        st.rerun()

    # Simple MCP server status
    api_client = get_api_client()
    try:
        mcp_servers = api_client.get_mcp_servers()
        if mcp_servers:
            st.sidebar.write("**Connected Servers:**")
            for server in mcp_servers[:5]:  # Show first 5 servers
                status_icon = "🟢" if server.get("is_healthy") else "🔴" if server.get("is_connected") else "⚪"
                st.sidebar.text(f"{status_icon} {server.get('name', 'Unknown')}")

            if len(mcp_servers) > 5:
                st.sidebar.text(f"... and {len(mcp_servers) - 5} more")
        else:
            st.sidebar.text("No servers connected")

    except Exception:
        st.sidebar.error("Unable to load servers")

    return page


def render_chat_interface():
    """Render Claude-like chat interface with MCP tool integration."""
    st.title("💬 Figma MCP Chat Interface")

    api_client = get_api_client()

    # Get available tools for display
    try:
        tools_data = api_client.get_available_tools()
        available_tools = tools_data.get('tools_by_server', {})
    except Exception:
        available_tools = {}

    # Show available tools in sidebar info
    if available_tools:
        total_tools = sum(len(tools) for tools in available_tools.values())
        st.info(f"🔧 {total_tools} MCP tools available from {len(available_tools)} servers")

        with st.expander("🛠️ Available Tools"):
            for server_name, tools in available_tools.items():
                if tools:
                    st.write(f"**{server_name}:**")
                    for tool in tools:
                        st.write(f"• `{tool.get('name')}` - {tool.get('description', 'No description')}")
    # else:
        # st.warning("⚠️ No MCP servers connected. Go to 'MCP Servers' to connect tools.")

    # Chat controls
    col1, col2 = st.columns([1, 1])
    with col1:
        use_mcp_tools = st.checkbox("Use MCP Tools", value=True, help="Allow the assistant to use connected MCP tools")
    with col2:
        if st.button("🗑️ Clear Chat"):
            st.session_state.conversation_history = []
            st.session_state.conversation_id = None
            st.rerun()

    # Display conversation history
    for message in st.session_state.conversation_history:
        with st.chat_message(message["role"]):
            st.write(message["content"])

            # Show tool calls if any
            if message.get("tool_calls"):
                with st.expander("🔧 Tool Calls"):
                    for tool_call in message["tool_calls"]:
                        st.code(f"Tool: {tool_call.get('name', 'Unknown')}")
                        if tool_call.get("arguments"):
                            st.json(tool_call["arguments"])

            # Show tool results if any
            if message.get("tool_results"):
                with st.expander("📊 Tool Results"):
                    for result in message["tool_results"]:
                        if result.get("success"):
                            st.success(f"✅ {result.get('tool_name', 'Tool')} executed successfully")
                            if result.get("result"):
                                st.json(result["result"])
                        else:
                            st.error(f"❌ {result.get('tool_name', 'Tool')} failed: {result.get('error', 'Unknown error')}")

    # Chat input
    if prompt := st.chat_input("Ask me anything about Figma operations..."):
        # Add user message to conversation
        user_message = {
            "role": "user",
            "content": prompt,
            "timestamp": datetime.now().isoformat()
        }
        st.session_state.conversation_history.append(user_message)

        # Display user message
        with st.chat_message("user"):
            st.write(prompt)

        # Process with assistant
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                try:
                    # Send chat message to backend
                    response = api_client.send_chat_message(
                        message=prompt,
                        conversation_id=st.session_state.conversation_id,
                        use_mcp_tools=use_mcp_tools
                    )

                    # Update conversation ID
                    st.session_state.conversation_id = response.get("conversation_id")

                    # Display response
                    assistant_response = response.get("response", "I'm sorry, I couldn't process your request.")
                    st.write(assistant_response)

                    # Show available tools that could be used
                    if response.get("available_tools"):
                        available_tool_names = response["available_tools"]
                        if available_tool_names:
                            st.info(f"💡 Available tools for this request: {', '.join(available_tool_names[:5])}")

                    # Create assistant message
                    assistant_message = {
                        "role": "assistant",
                        "content": assistant_response,
                        "timestamp": datetime.now().isoformat(),
                        "tool_calls": response.get("tool_calls", []),
                        "tool_results": response.get("tool_results", [])
                    }

                    # Add to conversation history
                    st.session_state.conversation_history.append(assistant_message)

                    # Add to task history for analytics
                    st.session_state.task_history.append({
                        "timestamp": datetime.now(),
                        "query": prompt,
                        "result": {
                            "success": True,
                            "response": assistant_response,
                            "tool_calls": len(response.get("tool_calls", [])),
                            "execution_time": 1.0  # Placeholder
                        },
                        "agent": "ChatAgent"
                    })

                except Exception as e:
                    error_message = f"I encountered an error: {str(e)}"
                    st.error(error_message)

                    # Add error message to conversation
                    error_assistant_message = {
                        "role": "assistant",
                        "content": error_message,
                        "timestamp": datetime.now().isoformat()
                    }
                    st.session_state.conversation_history.append(error_assistant_message)

    # Suggested prompts for new users
    # if not st.session_state.conversation_history:
    #     st.subheader("💡 Try asking me:")

    #     suggestions = [
    #         "Create a new Figma file for a mobile app",
    #         "Add a button component with hover states",
    #         "Export all icons from my design file",
    #         "Create a color palette for my brand",
    #         "Set up auto-layout for my design system",
    #         "Generate a prototype for user testing"
    #     ]

    #     cols = st.columns(2)
    #     for i, suggestion in enumerate(suggestions):
    #         with cols[i % 2]:
    #             if st.button(suggestion, key=f"suggestion_{i}"):
    #                 # Simulate clicking the suggestion
    #                 st.session_state.suggested_prompt = suggestion
    #                 st.rerun()


def render_agent_dashboard():
    """Render the agent dashboard with status and metrics."""
    st.title("🤖 Agent Dashboard")
    
    api_client = get_api_client()
    status = api_client.get_system_status()
    
    if not status:
        st.error("Unable to load agent status")
        return
    
    # Overview metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Agents", status.get("total_agents", 0))
    with col2:
        st.metric("Active Agents", status.get("active_agents", 0))
    with col3:
        st.metric("Busy Agents", status.get("busy_agents", 0))
    with col4:
        st.metric("MCP Connections", status.get("mcp_connections", {}).get("total_connections", 0))
    
    # Agent details
    st.subheader("Agent Details")
    
    agents = status.get("agents", {})
    if agents:
        # Create agent status dataframe
        agent_data = []
        for name, agent_info in agents.items():
            agent_data.append({
                "Agent": name,
                "Status": "Busy" if agent_info.get("is_busy") else "Available",
                "Total Tasks": agent_info.get("total_tasks", 0),
                "Success Rate": f"{(agent_info.get('successful_tasks', 0) / max(agent_info.get('total_tasks', 1), 1) * 100):.1f}%",
                "Capabilities": len(agent_info.get("capabilities", []))
            })
        
        df = pd.DataFrame(agent_data)
        st.dataframe(df, use_container_width=True)
        
        # Agent selection for detailed view
        selected_agent = st.selectbox("Select agent for details:", list(agents.keys()))
        
        if selected_agent:
            agent_info = agents[selected_agent]
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader(f"{selected_agent} Details")
                st.write(f"**Description:** {agent_info.get('description', 'N/A')}")
                st.write(f"**Status:** {'Busy' if agent_info.get('is_busy') else 'Available'}")
                st.write(f"**Current Task:** {agent_info.get('current_task', 'None')}")
                
                st.write("**Capabilities:**")
                for capability in agent_info.get("capabilities", []):
                    st.write(f"• {capability}")
            
            with col2:
                # Task history for selected agent
                history = api_client.get_agent_history(selected_agent)
                if history.get("history"):
                    st.subheader("Recent Tasks")
                    for task in history["history"][:5]:
                        status_icon = "✅" if task.get("status") == "completed" else "❌"
                        st.write(f"{status_icon} {task.get('task_type', 'Unknown')} - {task.get('duration', 0):.2f}s")


def render_system_status():
    """Render system status and monitoring."""
    st.title("📊 System Status")
    
    api_client = get_api_client()
    status = api_client.get_system_status()
    
    if not status:
        st.error("Unable to load system status")
        return
    
    # System health indicators
    st.subheader("System Health")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Agent health
        total_agents = status.get("total_agents", 0)
        active_agents = status.get("active_agents", 0)
        health_ratio = active_agents / max(total_agents, 1)
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=health_ratio * 100,
            title={"text": "Agent Health %"},
            gauge={"axis": {"range": [None, 100]},
                   "bar": {"color": "darkgreen" if health_ratio > 0.8 else "orange" if health_ratio > 0.5 else "red"}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # MCP connections
        mcp_stats = status.get("mcp_connections", {})
        total_connections = mcp_stats.get("total_connections", 0)
        healthy_connections = mcp_stats.get("healthy_connections", 0)
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=healthy_connections / max(total_connections, 1) * 100,
            title={"text": "MCP Health %"},
            gauge={"axis": {"range": [None, 100]}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col3:
        # Task success rate
        if st.session_state.task_history:
            successful_tasks = sum(1 for task in st.session_state.task_history if task["result"].get("success"))
            success_rate = successful_tasks / len(st.session_state.task_history) * 100
        else:
            success_rate = 0
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=success_rate,
            title={"text": "Success Rate %"},
            gauge={"axis": {"range": [None, 100]}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)


def render_task_history():
    """Render task history and analytics."""
    st.title("📈 Task History")
    
    if not st.session_state.task_history:
        st.info("No tasks executed yet. Try the chat interface!")
        return
    
    # Task history table
    st.subheader("Recent Tasks")
    
    history_data = []
    for task in st.session_state.task_history[-20:]:  # Last 20 tasks
        history_data.append({
            "Timestamp": task["timestamp"].strftime("%Y-%m-%d %H:%M:%S"),
            "Query": task["query"][:50] + "..." if len(task["query"]) > 50 else task["query"],
            "Agent": task["agent"],
            "Status": "✅ Success" if task["result"].get("success") else "❌ Failed",
            "Duration": f"{task['result'].get('execution_time', 0):.2f}s"
        })
    
    df = pd.DataFrame(history_data)
    st.dataframe(df, use_container_width=True)
    
    # Analytics
    if len(st.session_state.task_history) > 1:
        st.subheader("Analytics")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Tasks by agent
            agent_counts = {}
            for task in st.session_state.task_history:
                agent = task["agent"]
                agent_counts[agent] = agent_counts.get(agent, 0) + 1
            
            fig = px.pie(
                values=list(agent_counts.values()),
                names=list(agent_counts.keys()),
                title="Tasks by Agent"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Success rate over time
            success_data = []
            for i, task in enumerate(st.session_state.task_history):
                success_data.append({
                    "Task": i + 1,
                    "Success": 1 if task["result"].get("success") else 0
                })
            
            df_success = pd.DataFrame(success_data)
            df_success["Rolling Success Rate"] = df_success["Success"].rolling(window=5, min_periods=1).mean() * 100
            
            fig = px.line(
                df_success,
                x="Task",
                y="Rolling Success Rate",
                title="Success Rate Trend (5-task rolling average)"
            )
            st.plotly_chart(fig, use_container_width=True)


def render_mcp_servers():
    """Render simplified MCP server management interface."""
    st.title("MCP Servers")

    api_client = get_api_client()

    # Check if we should show the add server form
    show_add_form = st.session_state.get('show_add_server', False)

    # Get current servers
    try:
        servers = api_client.get_mcp_servers()
    except Exception as e:
        st.error(f"Failed to load MCP servers: {e}")
        servers = []

    # Add server button (if not already showing form)
    if not show_add_form:
        if st.button("➕ Add New MCP Server", type="primary"):
            st.session_state.show_add_server = True
            st.rerun()

    # Add new server form (show if button clicked or sidebar button clicked)
    if show_add_form:
        st.subheader("Connect New MCP Server")



        # Add helpful information about MCP servers
        st.info("""
        **📋 MCP Server Requirements:**
        - The command must start a process that implements the MCP (Model Context Protocol)
        - Common examples: Figma MCP servers, file system tools, API integrations
        - The process should respond to MCP initialization messages
        """)

        with st.form("add_mcp_server"):
            server_name = st.text_input(
                "Server Name *",
                placeholder="figma-mcp",
                help="Unique name to identify this MCP server"
            )

            command = st.text_input(
                "Command *",
                placeholder="bun",
                help="Executable command (e.g., 'bun', 'npx', 'node', 'python')"
            )

            args_text = st.text_input(
                "Arguments",
                placeholder="E:/claude-talk-to-figma-mcp/src/talk_to_figma_mcp/server.ts",
                help="Command arguments - use full paths for script files"
            )

            env_text = st.text_area(
                "Environment Variables",
                placeholder="FIGMA_ACCESS_TOKEN=your_token_here\nDEBUG=true",
                help="One variable per line in KEY=VALUE format"
            )

            col1, col2 = st.columns(2)
            with col1:
                submitted = st.form_submit_button("Connect Server", type="primary")
            with col2:
                cancelled = st.form_submit_button("Cancel")

            if cancelled:
                st.session_state.show_add_server = False
                st.rerun()

            if submitted:
                if not server_name or not command:
                    st.error("❌ Server name and command are required")
                elif not server_name.replace('-', '').replace('_', '').isalnum():
                    st.error("❌ Server name should only contain letters, numbers, hyphens, and underscores")
                else:
                    # Parse arguments - handle different command types properly
                    args = []
                    if args_text:
                        # For file paths with spaces, try to preserve them
                        if args_text.strip().startswith('"') and args_text.strip().endswith('"'):
                            # Handle quoted arguments
                            args = [args_text.strip().strip('"')]
                        elif '/' in args_text or '\\' in args_text:
                            # Likely a file path, treat as single argument
                            args = [args_text.strip()]
                        else:
                            # Split by spaces for multiple arguments
                            args = [arg.strip() for arg in args_text.split() if arg.strip()]

                    # Parse environment variables
                    env = {}
                    if env_text:
                        for line in env_text.split('\n'):
                            if '=' in line and line.strip():
                                key, value = line.split('=', 1)
                                env[key.strip()] = value.strip()

                    server_config = {
                        "name": server_name,
                        "command": command,
                        "args": args,
                        "env": env,
                        "description": f"MCP server: {server_name}"
                    }

                    try:
                        with st.spinner(f"Connecting to {server_name}..."):
                            result = api_client.connect_mcp_server(server_config)

                        if result.get('success'):
                            st.success(f"✅ Successfully connected to {server_name}!")
                            if result.get('tools'):
                                st.info(f"🔧 Found {len(result['tools'])} tools available")
                            st.session_state.show_add_server = False
                            st.rerun()
                        else:
                            error_msg = result.get('message', 'Unknown error')
                            st.error(f"❌ Failed to connect: {error_msg}")

                            # Provide specific help for common errors
                            if "Command" in error_msg and "not found" in error_msg:
                                st.warning("💡 **Tip:** The command was not found. Make sure the runtime (bun, node, python, etc.) is installed and accessible from your command line.")
                            elif "Permission denied" in error_msg:
                                st.warning("💡 **Tip:** Permission denied. Try running as administrator or check file permissions.")
                            elif "initialization timed out" in error_msg:
                                st.warning("💡 **Tip:** The process started but didn't respond as an MCP server. Make sure you're connecting to a valid MCP server that implements the MCP protocol.")
                            elif "not responding as a valid MCP server" in error_msg:
                                st.warning("💡 **Tip:** The command executed but doesn't implement the MCP protocol. Ensure you're using a proper MCP server implementation.")
                            elif "File not found" in error_msg:
                                st.warning("💡 **Tip:** The specified file path doesn't exist. Check that the path is correct and the file exists.")
                            elif command in ["bun", "node"] and any(ext in str(args) for ext in [".ts", ".js"]):
                                st.warning("💡 **Tip:** For TypeScript files, use 'bun' for .ts files or 'node' for .js files. Make sure the file path is correct and the file exists.")
                            elif command == "npx":
                                st.warning("💡 **Tip:** NPX not found. Install Node.js and npm, then ensure npx is in your PATH. You can also try using 'node' directly if you have the package installed locally.")
                            else:
                                st.warning("💡 **Tip:** Connection failed. Ensure the command is a valid MCP server implementation and all required dependencies are installed.")

                    except Exception as e:
                        st.error(f"❌ Connection error: {e}")

        st.divider()

    # Simple server list
    if servers:
        st.subheader("Connected Servers")
        for server in servers:
            with st.container():
                col1, col2, col3 = st.columns([1, 3, 1])

                with col1:
                    # Status indicator
                    if server.get('is_healthy'):
                        st.success("●")
                    elif server.get('is_connected'):
                        st.warning("●")
                    else:
                        st.error("●")

                with col2:
                    st.write(f"**{server.get('name', 'Unknown')}**")
                    st.caption(f"{server.get('tools_count', 0)} tools available")

                with col3:
                    if st.button("Disconnect", key=f"disconnect_{server.get('name')}", type="secondary"):
                        try:
                            result = api_client.disconnect_mcp_server(server.get('name'))
                            if result.get('success'):
                                st.success(f"Disconnected from {server.get('name')}")
                                st.rerun()
                            else:
                                st.error(f"Failed to disconnect: {result.get('message')}")
                        except Exception as e:
                            st.error(f"Error: {e}")

                st.divider()
    else:
        st.info("No MCP servers connected. Click 'Add New MCP Server' to get started.")


def main():
    """Main Streamlit application."""
    initialize_session_state()

    # Render sidebar and get selected page
    page = render_sidebar()

    # Handle page override from sidebar button
    if hasattr(st.session_state, 'selected_page'):
        page = st.session_state.selected_page
        del st.session_state.selected_page

    # Render selected page - Simplified to core functionality
    if page == "Chat Interface":
        render_chat_interface()
    elif page == "MCP Servers":
        render_mcp_servers()
    else:
        # Default to chat interface
        render_chat_interface()


if __name__ == "__main__":
    main()
