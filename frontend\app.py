"""
Streamlit frontend for the Figma MCP Multi-Agent Client.

This module provides a web interface with multi-agent dashboard, chat interface,
progress tracking, and real-time updates.
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add parent directory to Python path for imports
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

import streamlit as st
import httpx
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

# Now we can import from the app package
from app.core.config import settings


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Figma MCP Multi-Agent Client",
    page_icon="🎨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if "conversation_history" not in st.session_state:
    st.session_state.conversation_history = []
if "conversation_id" not in st.session_state:
    st.session_state.conversation_id = None
if "mcp_servers" not in st.session_state:
    st.session_state.mcp_servers = []
if "available_tools" not in st.session_state:
    st.session_state.available_tools = {}

# API client configuration
API_BASE_URL = f"http://{settings.api_host}:{settings.api_port}"


class APIClient:
    """HTTP client for communicating with the FastAPI backend."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.client = httpx.Client(timeout=30.0)
    
    def execute_task(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a task through the API."""
        try:
            response = self.client.post(
                f"{self.base_url}/tasks/execute",
                json={
                    "query": query,
                    "context": context or {},
                    "timeout": 60.0
                }
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"API call failed: {e}")
            return {"success": False, "error_message": str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get system status from the API."""
        try:
            response = self.client.get(f"{self.base_url}/agents/status")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            return {}
    
    def get_agent_history(self, agent_name: str, limit: int = 10) -> Dict[str, Any]:
        """Get agent task history."""
        try:
            response = self.client.get(
                f"{self.base_url}/agents/{agent_name}/history",
                params={"limit": limit}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get agent history: {e}")
            return {"history": []}

    def get_mcp_servers(self) -> List[Dict[str, Any]]:
        """Get list of MCP servers."""
        try:
            response = self.client.get(f"{self.base_url}/mcp/servers")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get MCP servers: {e}")
            return []

    def connect_mcp_server(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """Connect to an MCP server."""
        try:
            response = self.client.post(
                f"{self.base_url}/mcp/servers/connect",
                json={"server_config": server_config}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to connect MCP server: {e}")
            return {"success": False, "message": str(e)}

    def disconnect_mcp_server(self, server_name: str) -> Dict[str, Any]:
        """Disconnect from an MCP server."""
        try:
            response = self.client.delete(f"{self.base_url}/mcp/servers/{server_name}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to disconnect MCP server: {e}")
            return {"success": False, "message": str(e)}

    def get_available_tools(self) -> Dict[str, Any]:
        """Get available MCP tools."""
        try:
            response = self.client.get(f"{self.base_url}/chat/tools")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get available tools: {e}")
            return {"tools_by_server": {}}

    def send_chat_message(self, message: str, conversation_id: Optional[str] = None,
                         use_mcp_tools: bool = True) -> Dict[str, Any]:
        """Send a chat message."""
        try:
            payload = {
                "message": message,
                "conversation_id": conversation_id,
                "use_mcp_tools": use_mcp_tools
            }
            response = self.client.post(f"{self.base_url}/chat", json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to send chat message: {e}")
            return {
                "response": f"Connection error: {str(e)}",
                "conversation_id": conversation_id or "error",
                "tool_calls": [],
                "tool_results": [],
                "available_tools": []
            }


# Initialize API client
@st.cache_resource
def get_api_client():
    return APIClient(API_BASE_URL)


# Initialize session state
def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "task_history" not in st.session_state:
        st.session_state.task_history = []
    if "selected_agent" not in st.session_state:
        st.session_state.selected_agent = None


def render_sidebar():
    """Render the sidebar with navigation and system status."""
    st.sidebar.title("🎨 Figma MCP Client")
    
    # Navigation
    page = st.sidebar.selectbox(
        "Navigate",
        ["Chat Interface", "MCP Servers", "Agent Dashboard", "System Status", "Task History"]
    )
    
    # System status summary
    st.sidebar.subheader("System Status")
    api_client = get_api_client()

    try:
        status = api_client.get_system_status()
        if status:
            st.sidebar.metric("Active Agents", status.get("active_agents", 0))
            st.sidebar.metric("Busy Agents", status.get("busy_agents", 0))
            st.sidebar.metric("Total Tasks", len(st.session_state.task_history))
        else:
            st.sidebar.error("Unable to connect to API")
    except Exception as e:
        st.sidebar.error(f"API Error: {str(e)}")

    # MCP Servers status
    st.sidebar.subheader("MCP Servers")
    try:
        mcp_servers = api_client.get_mcp_servers()
        connected_count = sum(1 for server in mcp_servers if server.get("is_connected", False))
        healthy_count = sum(1 for server in mcp_servers if server.get("is_healthy", False))

        st.sidebar.metric("Connected Servers", f"{connected_count}/{len(mcp_servers)}")
        st.sidebar.metric("Healthy Servers", healthy_count)

        # Show server status indicators
        for server in mcp_servers[:3]:  # Show first 3 servers
            status_icon = "🟢" if server.get("is_healthy") else "🔴" if server.get("is_connected") else "⚪"
            st.sidebar.text(f"{status_icon} {server.get('name', 'Unknown')}")

        if len(mcp_servers) > 3:
            st.sidebar.text(f"... and {len(mcp_servers) - 3} more")

    except Exception as e:
        st.sidebar.error(f"MCP Error: {str(e)}")

    return page


def render_chat_interface():
    """Render Claude-like chat interface with MCP tool integration."""
    st.title("💬 Figma MCP Chat Interface")

    api_client = get_api_client()

    # Get available tools for display
    try:
        tools_data = api_client.get_available_tools()
        available_tools = tools_data.get('tools_by_server', {})
    except Exception:
        available_tools = {}

    # Show available tools in sidebar info
    if available_tools:
        total_tools = sum(len(tools) for tools in available_tools.values())
        st.info(f"🔧 {total_tools} MCP tools available from {len(available_tools)} servers")

        with st.expander("🛠️ Available Tools"):
            for server_name, tools in available_tools.items():
                if tools:
                    st.write(f"**{server_name}:**")
                    for tool in tools:
                        st.write(f"• `{tool.get('name')}` - {tool.get('description', 'No description')}")
    else:
        st.warning("⚠️ No MCP servers connected. Go to 'MCP Servers' to connect tools.")

    # Chat controls
    col1, col2 = st.columns([1, 1])
    with col1:
        use_mcp_tools = st.checkbox("Use MCP Tools", value=True, help="Allow the assistant to use connected MCP tools")
    with col2:
        if st.button("🗑️ Clear Chat"):
            st.session_state.conversation_history = []
            st.session_state.conversation_id = None
            st.rerun()

    # Display conversation history
    for message in st.session_state.conversation_history:
        with st.chat_message(message["role"]):
            st.write(message["content"])

            # Show tool calls if any
            if message.get("tool_calls"):
                with st.expander("🔧 Tool Calls"):
                    for tool_call in message["tool_calls"]:
                        st.code(f"Tool: {tool_call.get('name', 'Unknown')}")
                        if tool_call.get("arguments"):
                            st.json(tool_call["arguments"])

            # Show tool results if any
            if message.get("tool_results"):
                with st.expander("📊 Tool Results"):
                    for result in message["tool_results"]:
                        if result.get("success"):
                            st.success(f"✅ {result.get('tool_name', 'Tool')} executed successfully")
                            if result.get("result"):
                                st.json(result["result"])
                        else:
                            st.error(f"❌ {result.get('tool_name', 'Tool')} failed: {result.get('error', 'Unknown error')}")

    # Chat input
    if prompt := st.chat_input("Ask me anything about Figma operations..."):
        # Add user message to conversation
        user_message = {
            "role": "user",
            "content": prompt,
            "timestamp": datetime.now().isoformat()
        }
        st.session_state.conversation_history.append(user_message)

        # Display user message
        with st.chat_message("user"):
            st.write(prompt)

        # Process with assistant
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                try:
                    # Send chat message to backend
                    response = api_client.send_chat_message(
                        message=prompt,
                        conversation_id=st.session_state.conversation_id,
                        use_mcp_tools=use_mcp_tools
                    )

                    # Update conversation ID
                    st.session_state.conversation_id = response.get("conversation_id")

                    # Display response
                    assistant_response = response.get("response", "I'm sorry, I couldn't process your request.")
                    st.write(assistant_response)

                    # Show available tools that could be used
                    if response.get("available_tools"):
                        available_tool_names = response["available_tools"]
                        if available_tool_names:
                            st.info(f"💡 Available tools for this request: {', '.join(available_tool_names[:5])}")

                    # Create assistant message
                    assistant_message = {
                        "role": "assistant",
                        "content": assistant_response,
                        "timestamp": datetime.now().isoformat(),
                        "tool_calls": response.get("tool_calls", []),
                        "tool_results": response.get("tool_results", [])
                    }

                    # Add to conversation history
                    st.session_state.conversation_history.append(assistant_message)

                    # Add to task history for analytics
                    st.session_state.task_history.append({
                        "timestamp": datetime.now(),
                        "query": prompt,
                        "result": {
                            "success": True,
                            "response": assistant_response,
                            "tool_calls": len(response.get("tool_calls", [])),
                            "execution_time": 1.0  # Placeholder
                        },
                        "agent": "ChatAgent"
                    })

                except Exception as e:
                    error_message = f"I encountered an error: {str(e)}"
                    st.error(error_message)

                    # Add error message to conversation
                    error_assistant_message = {
                        "role": "assistant",
                        "content": error_message,
                        "timestamp": datetime.now().isoformat()
                    }
                    st.session_state.conversation_history.append(error_assistant_message)

    # Suggested prompts for new users
    if not st.session_state.conversation_history:
        st.subheader("💡 Try asking me:")

        suggestions = [
            "Create a new Figma file for a mobile app",
            "Add a button component with hover states",
            "Export all icons from my design file",
            "Create a color palette for my brand",
            "Set up auto-layout for my design system",
            "Generate a prototype for user testing"
        ]

        cols = st.columns(2)
        for i, suggestion in enumerate(suggestions):
            with cols[i % 2]:
                if st.button(suggestion, key=f"suggestion_{i}"):
                    # Simulate clicking the suggestion
                    st.session_state.suggested_prompt = suggestion
                    st.rerun()


def render_agent_dashboard():
    """Render the agent dashboard with status and metrics."""
    st.title("🤖 Agent Dashboard")
    
    api_client = get_api_client()
    status = api_client.get_system_status()
    
    if not status:
        st.error("Unable to load agent status")
        return
    
    # Overview metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Agents", status.get("total_agents", 0))
    with col2:
        st.metric("Active Agents", status.get("active_agents", 0))
    with col3:
        st.metric("Busy Agents", status.get("busy_agents", 0))
    with col4:
        st.metric("MCP Connections", status.get("mcp_connections", {}).get("total_connections", 0))
    
    # Agent details
    st.subheader("Agent Details")
    
    agents = status.get("agents", {})
    if agents:
        # Create agent status dataframe
        agent_data = []
        for name, agent_info in agents.items():
            agent_data.append({
                "Agent": name,
                "Status": "Busy" if agent_info.get("is_busy") else "Available",
                "Total Tasks": agent_info.get("total_tasks", 0),
                "Success Rate": f"{(agent_info.get('successful_tasks', 0) / max(agent_info.get('total_tasks', 1), 1) * 100):.1f}%",
                "Capabilities": len(agent_info.get("capabilities", []))
            })
        
        df = pd.DataFrame(agent_data)
        st.dataframe(df, use_container_width=True)
        
        # Agent selection for detailed view
        selected_agent = st.selectbox("Select agent for details:", list(agents.keys()))
        
        if selected_agent:
            agent_info = agents[selected_agent]
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader(f"{selected_agent} Details")
                st.write(f"**Description:** {agent_info.get('description', 'N/A')}")
                st.write(f"**Status:** {'Busy' if agent_info.get('is_busy') else 'Available'}")
                st.write(f"**Current Task:** {agent_info.get('current_task', 'None')}")
                
                st.write("**Capabilities:**")
                for capability in agent_info.get("capabilities", []):
                    st.write(f"• {capability}")
            
            with col2:
                # Task history for selected agent
                history = api_client.get_agent_history(selected_agent)
                if history.get("history"):
                    st.subheader("Recent Tasks")
                    for task in history["history"][:5]:
                        status_icon = "✅" if task.get("status") == "completed" else "❌"
                        st.write(f"{status_icon} {task.get('task_type', 'Unknown')} - {task.get('duration', 0):.2f}s")


def render_system_status():
    """Render system status and monitoring."""
    st.title("📊 System Status")
    
    api_client = get_api_client()
    status = api_client.get_system_status()
    
    if not status:
        st.error("Unable to load system status")
        return
    
    # System health indicators
    st.subheader("System Health")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Agent health
        total_agents = status.get("total_agents", 0)
        active_agents = status.get("active_agents", 0)
        health_ratio = active_agents / max(total_agents, 1)
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=health_ratio * 100,
            title={"text": "Agent Health %"},
            gauge={"axis": {"range": [None, 100]},
                   "bar": {"color": "darkgreen" if health_ratio > 0.8 else "orange" if health_ratio > 0.5 else "red"}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # MCP connections
        mcp_stats = status.get("mcp_connections", {})
        total_connections = mcp_stats.get("total_connections", 0)
        healthy_connections = mcp_stats.get("healthy_connections", 0)
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=healthy_connections / max(total_connections, 1) * 100,
            title={"text": "MCP Health %"},
            gauge={"axis": {"range": [None, 100]}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col3:
        # Task success rate
        if st.session_state.task_history:
            successful_tasks = sum(1 for task in st.session_state.task_history if task["result"].get("success"))
            success_rate = successful_tasks / len(st.session_state.task_history) * 100
        else:
            success_rate = 0
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=success_rate,
            title={"text": "Success Rate %"},
            gauge={"axis": {"range": [None, 100]}}
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)


def render_task_history():
    """Render task history and analytics."""
    st.title("📈 Task History")
    
    if not st.session_state.task_history:
        st.info("No tasks executed yet. Try the chat interface!")
        return
    
    # Task history table
    st.subheader("Recent Tasks")
    
    history_data = []
    for task in st.session_state.task_history[-20:]:  # Last 20 tasks
        history_data.append({
            "Timestamp": task["timestamp"].strftime("%Y-%m-%d %H:%M:%S"),
            "Query": task["query"][:50] + "..." if len(task["query"]) > 50 else task["query"],
            "Agent": task["agent"],
            "Status": "✅ Success" if task["result"].get("success") else "❌ Failed",
            "Duration": f"{task['result'].get('execution_time', 0):.2f}s"
        })
    
    df = pd.DataFrame(history_data)
    st.dataframe(df, use_container_width=True)
    
    # Analytics
    if len(st.session_state.task_history) > 1:
        st.subheader("Analytics")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Tasks by agent
            agent_counts = {}
            for task in st.session_state.task_history:
                agent = task["agent"]
                agent_counts[agent] = agent_counts.get(agent, 0) + 1
            
            fig = px.pie(
                values=list(agent_counts.values()),
                names=list(agent_counts.keys()),
                title="Tasks by Agent"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Success rate over time
            success_data = []
            for i, task in enumerate(st.session_state.task_history):
                success_data.append({
                    "Task": i + 1,
                    "Success": 1 if task["result"].get("success") else 0
                })
            
            df_success = pd.DataFrame(success_data)
            df_success["Rolling Success Rate"] = df_success["Success"].rolling(window=5, min_periods=1).mean() * 100
            
            fig = px.line(
                df_success,
                x="Task",
                y="Rolling Success Rate",
                title="Success Rate Trend (5-task rolling average)"
            )
            st.plotly_chart(fig, use_container_width=True)


def render_mcp_servers():
    """Render MCP server management interface."""
    st.title("🔌 MCP Server Management")
    st.write("Connect and manage Model Context Protocol (MCP) servers")

    api_client = get_api_client()

    # Refresh servers button
    col1, col2 = st.columns([1, 4])
    with col1:
        if st.button("🔄 Refresh", key="refresh_servers"):
            st.rerun()

    # Get current servers
    try:
        servers = api_client.get_mcp_servers()
        tools_data = api_client.get_available_tools()
    except Exception as e:
        st.error(f"Failed to load MCP servers: {e}")
        servers = []
        tools_data = {"tools_by_server": {}}

    # Server status overview
    st.subheader("📊 Server Status Overview")

    if servers:
        # Create metrics
        connected_count = sum(1 for s in servers if s.get("is_connected", False))
        healthy_count = sum(1 for s in servers if s.get("is_healthy", False))
        total_tools = sum(s.get("tools_count", 0) for s in servers)

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Servers", len(servers))
        with col2:
            st.metric("Connected", connected_count)
        with col3:
            st.metric("Healthy", healthy_count)
        with col4:
            st.metric("Available Tools", total_tools)

        # Server list
        st.subheader("🖥️ Connected Servers")

        for server in servers:
            with st.expander(f"{'🟢' if server.get('is_healthy') else '🔴' if server.get('is_connected') else '⚪'} {server.get('name', 'Unknown')}"):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.write(f"**Status:** {'Healthy' if server.get('is_healthy') else 'Connected' if server.get('is_connected') else 'Disconnected'}")
                    st.write(f"**Tools:** {server.get('tools_count', 0)}")
                    if server.get('last_ping'):
                        st.write(f"**Last Ping:** {server.get('last_ping')}")
                    if server.get('error_message'):
                        st.error(f"**Error:** {server.get('error_message')}")

                with col2:
                    if st.button("🗑️ Disconnect", key=f"disconnect_{server.get('name')}"):
                        try:
                            result = api_client.disconnect_mcp_server(server.get('name'))
                            if result.get('success'):
                                st.success(f"Disconnected from {server.get('name')}")
                                st.rerun()
                            else:
                                st.error(f"Failed to disconnect: {result.get('message')}")
                        except Exception as e:
                            st.error(f"Error: {e}")

                # Show tools for this server
                server_name = server.get('name')
                if server_name in tools_data.get('tools_by_server', {}):
                    tools = tools_data['tools_by_server'][server_name]
                    if tools:
                        st.write("**Available Tools:**")
                        for tool in tools:
                            st.write(f"• `{tool.get('name')}` - {tool.get('description', 'No description')}")
    else:
        st.info("No MCP servers connected. Add a server below to get started.")

    # Add new server section
    st.subheader("➕ Connect New MCP Server")

    with st.form("add_mcp_server"):
        col1, col2 = st.columns(2)

        with col1:
            server_name = st.text_input("Server Name", placeholder="my-figma-server")
            command = st.text_input("Command", placeholder="node server.js")

        with col2:
            args_text = st.text_area("Arguments (one per line)", placeholder="--port 3001\n--verbose")
            env_text = st.text_area("Environment Variables (KEY=VALUE, one per line)",
                                   placeholder="FIGMA_TOKEN=your_token\nDEBUG=true")

        description = st.text_input("Description (optional)", placeholder="Figma MCP server for design operations")

        submitted = st.form_submit_button("🔌 Connect Server")

        if submitted:
            if not server_name or not command:
                st.error("Server name and command are required")
            else:
                # Parse arguments
                args = [arg.strip() for arg in args_text.split('\n') if arg.strip()] if args_text else []

                # Parse environment variables
                env = {}
                if env_text:
                    for line in env_text.split('\n'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            env[key.strip()] = value.strip()

                server_config = {
                    "name": server_name,
                    "command": command,
                    "args": args,
                    "env": env,
                    "description": description
                }

                try:
                    with st.spinner(f"Connecting to {server_name}..."):
                        result = api_client.connect_mcp_server(server_config)

                    if result.get('success'):
                        st.success(f"Successfully connected to {server_name}!")
                        if result.get('tools'):
                            st.write(f"Found {len(result['tools'])} tools:")
                            for tool in result['tools']:
                                st.write(f"• {tool.get('name')} - {tool.get('description', 'No description')}")
                        st.rerun()
                    else:
                        st.error(f"Failed to connect: {result.get('message')}")

                except Exception as e:
                    st.error(f"Connection error: {e}")

    # Quick connect presets
    st.subheader("🚀 Quick Connect Presets")

    presets = {
        "Figma MCP Server": {
            "command": "npx",
            "args": ["@figma/mcp-server"],
            "env": {"FIGMA_ACCESS_TOKEN": "your_figma_token_here"},
            "description": "Official Figma MCP server"
        },
        "Local Development Server": {
            "command": "node",
            "args": ["server.js", "--port", "3001"],
            "env": {"NODE_ENV": "development"},
            "description": "Local development MCP server"
        }
    }

    col1, col2 = st.columns(2)

    for i, (preset_name, preset_config) in enumerate(presets.items()):
        with col1 if i % 2 == 0 else col2:
            with st.container():
                st.write(f"**{preset_name}**")
                st.write(preset_config["description"])
                if st.button(f"Use Preset", key=f"preset_{i}"):
                    # Pre-fill the form with preset values
                    st.session_state.preset_config = preset_config
                    st.session_state.preset_name = preset_name
                    st.info(f"Preset loaded! Scroll up to customize and connect.")


def main():
    """Main Streamlit application."""
    initialize_session_state()
    
    # Render sidebar and get selected page
    page = render_sidebar()
    
    # Render selected page
    if page == "Chat Interface":
        render_chat_interface()
    elif page == "MCP Servers":
        render_mcp_servers()
    elif page == "Agent Dashboard":
        render_agent_dashboard()
    elif page == "System Status":
        render_system_status()
    elif page == "Task History":
        render_task_history()


if __name__ == "__main__":
    main()
