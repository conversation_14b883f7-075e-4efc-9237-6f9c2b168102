"""
Base agent class and interfaces for the Figma MCP client.

This module defines the abstract base class and common interfaces
that all specialist agents must implement.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import time
import uuid

from mcp import ClientSession
from mcp.types import Tool, Resource, Prompt

from ..core.config import settings
from ..core.exceptions import Agent<PERSON>rror, TaskExecutionError, AgentBusyError
from ..core.mcp_client import mcp_client_manager


logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class AgentTask:
    """Represents a task for agent execution."""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    task_type: str = ""
    description: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    status: TaskStatus = TaskStatus.PENDING
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None
    result: Optional[Any] = None
    
    @property
    def duration(self) -> Optional[float]:
        """Get task execution duration."""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


@dataclass
class AgentResponse:
    """Response from agent task execution."""
    success: bool
    result: Optional[Any] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary."""
        return {
            "success": self.success,
            "result": self.result,
            "error_message": self.error_message,
            "metadata": self.metadata,
            "execution_time": self.execution_time
        }


class BaseAgent(ABC):
    """
    Abstract base class for all Figma MCP agents.
    
    Provides common functionality for MCP integration, task management,
    and error handling that all specialist agents inherit.
    """
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.is_busy = False
        self.current_task: Optional[AgentTask] = None
        self.task_history: List[AgentTask] = []
        self.capabilities: List[str] = []
        self._session_cache: Dict[str, ClientSession] = {}
        
        logger.info(f"Initialized agent: {self.name}")
    
    @abstractmethod
    async def can_handle(self, task: str) -> bool:
        """
        Determine if this agent can handle the given task.
        
        Args:
            task: Task description or type
            
        Returns:
            bool: True if agent can handle the task
        """
        pass
    
    @abstractmethod
    async def execute(self, task: AgentTask) -> AgentResponse:
        """
        Execute the given task.
        
        Args:
            task: Task to execute
            
        Returns:
            AgentResponse: Execution result
        """
        pass
    
    @abstractmethod
    async def get_available_tools(self) -> List[Tool]:
        """
        Get list of available MCP tools for this agent.
        
        Returns:
            List[Tool]: Available MCP tools
        """
        pass
    
    async def get_available_resources(self) -> List[Resource]:
        """
        Get list of available MCP resources for this agent.
        
        Returns:
            List[Resource]: Available MCP resources
        """
        return []
    
    async def get_available_prompts(self) -> List[Prompt]:
        """
        Get list of available MCP prompts for this agent.
        
        Returns:
            List[Prompt]: Available MCP prompts
        """
        return []
    
    async def validate_task(self, task: AgentTask) -> bool:
        """
        Validate task parameters and requirements.
        
        Args:
            task: Task to validate
            
        Returns:
            bool: True if task is valid
        """
        if not task.task_type:
            return False
        
        # Check if agent can handle this task type
        return await self.can_handle(task.task_type)
    
    async def execute_with_timeout(
        self,
        task: AgentTask,
        timeout: Optional[float] = None
    ) -> AgentResponse:
        """
        Execute task with timeout protection.
        
        Args:
            task: Task to execute
            timeout: Timeout in seconds (defaults to agent_timeout setting)
            
        Returns:
            AgentResponse: Execution result
            
        Raises:
            AgentBusyError: If agent is already busy
            TaskExecutionError: If task execution fails
        """
        if self.is_busy:
            raise AgentBusyError(
                f"Agent {self.name} is busy with task {self.current_task.task_id}",
                agent_name=self.name,
                task_id=task.task_id
            )
        
        # Validate task
        if not await self.validate_task(task):
            return AgentResponse(
                success=False,
                error_message=f"Task validation failed for {task.task_type}"
            )
        
        self.is_busy = True
        self.current_task = task
        task.status = TaskStatus.RUNNING
        task.started_at = time.time()
        
        timeout = timeout or settings.agent_timeout
        
        try:
            # Execute with timeout
            response = await asyncio.wait_for(
                self.execute(task),
                timeout=timeout
            )
            
            task.status = TaskStatus.COMPLETED
            task.result = response.result
            response.execution_time = task.duration
            
            logger.info(f"Agent {self.name} completed task {task.task_id}")
            return response
            
        except asyncio.TimeoutError:
            task.status = TaskStatus.FAILED
            task.error_message = f"Task timed out after {timeout} seconds"
            
            error_response = AgentResponse(
                success=False,
                error_message=task.error_message
            )
            
            logger.error(f"Agent {self.name} task {task.task_id} timed out")
            return error_response
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            
            error_response = AgentResponse(
                success=False,
                error_message=task.error_message
            )
            
            logger.error(f"Agent {self.name} task {task.task_id} failed: {e}")
            return error_response
            
        finally:
            task.completed_at = time.time()
            self.task_history.append(task)
            self.current_task = None
            self.is_busy = False
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current agent status.
        
        Returns:
            Dict[str, Any]: Agent status information
        """
        return {
            "name": self.name,
            "description": self.description,
            "is_busy": self.is_busy,
            "current_task": self.current_task.task_id if self.current_task else None,
            "capabilities": self.capabilities,
            "total_tasks": len(self.task_history),
            "successful_tasks": len([
                t for t in self.task_history if t.status == TaskStatus.COMPLETED
            ]),
            "failed_tasks": len([
                t for t in self.task_history if t.status == TaskStatus.FAILED
            ])
        }
    
    def get_task_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent task history.
        
        Args:
            limit: Maximum number of tasks to return
            
        Returns:
            List[Dict[str, Any]]: Recent task history
        """
        recent_tasks = sorted(
            self.task_history,
            key=lambda t: t.created_at,
            reverse=True
        )[:limit]
        
        return [
            {
                "task_id": task.task_id,
                "task_type": task.task_type,
                "status": task.status.value,
                "created_at": task.created_at,
                "duration": task.duration,
                "error_message": task.error_message
            }
            for task in recent_tasks
        ]


class AgentFactory:
    """
    Factory class for creating and managing agent instances.

    Implements the Factory pattern for dynamic agent instantiation
    and provides agent registry functionality.
    """

    _agents: Dict[str, type] = {}
    _instances: Dict[str, BaseAgent] = {}

    @classmethod
    def register_agent(cls, agent_class: type, name: Optional[str] = None):
        """
        Register an agent class.

        Args:
            agent_class: Agent class to register
            name: Optional name override
        """
        agent_name = name or agent_class.__name__
        cls._agents[agent_name] = agent_class
        logger.info(f"Registered agent class: {agent_name}")

    @classmethod
    def create_agent(cls, agent_name: str, **kwargs) -> BaseAgent:
        """
        Create an agent instance.

        Args:
            agent_name: Name of the agent to create
            **kwargs: Additional arguments for agent initialization

        Returns:
            BaseAgent: Created agent instance

        Raises:
            AgentError: If agent class is not registered
        """
        if agent_name not in cls._agents:
            raise AgentError(f"Agent class {agent_name} not registered")

        agent_class = cls._agents[agent_name]
        instance = agent_class(**kwargs)
        cls._instances[instance.name] = instance

        logger.info(f"Created agent instance: {instance.name}")
        return instance

    @classmethod
    def get_agent(cls, agent_name: str) -> Optional[BaseAgent]:
        """
        Get existing agent instance.

        Args:
            agent_name: Name of the agent

        Returns:
            BaseAgent: Agent instance or None if not found
        """
        return cls._instances.get(agent_name)

    @classmethod
    def list_available_agents(cls) -> List[str]:
        """
        List all registered agent classes.

        Returns:
            List[str]: List of registered agent names
        """
        return list(cls._agents.keys())

    @classmethod
    def list_active_agents(cls) -> List[str]:
        """
        List all active agent instances.

        Returns:
            List[str]: List of active agent names
        """
        return list(cls._instances.keys())

    @classmethod
    def get_agent_status_summary(cls) -> Dict[str, Any]:
        """
        Get status summary for all active agents.

        Returns:
            Dict[str, Any]: Status summary
        """
        return {
            "total_registered": len(cls._agents),
            "total_active": len(cls._instances),
            "busy_agents": len([
                agent for agent in cls._instances.values() if agent.is_busy
            ]),
            "agents": {
                name: agent.get_status()
                for name, agent in cls._instances.items()
            }
        }
