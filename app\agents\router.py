"""
Router Agent for the Figma MCP client.

This module implements the central orchestrator agent that uses Google Gemini LLM
for intent classification and dispatches tasks to appropriate specialist agents.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import json
import re

import google.generativeai as genai
from google.generativeai.types import <PERSON>rmCategory, HarmBlockThreshold

from .base import BaseAgent, AgentTask, AgentResponse, AgentFactory
from ..core.config import settings
from ..core.exceptions import Agent<PERSON>rror, TaskExecutionError


logger = logging.getLogger(__name__)


class RouterAgent(BaseAgent):
    """
    Central orchestrator agent that classifies user intents and routes tasks
    to appropriate specialist agents using Google Gemini LLM.
    """
    
    def __init__(self):
        super().__init__(
            name="RouterAgent",
            description="Central orchestrator for intent classification and task routing"
        )
        
        # Configure Gemini API
        genai.configure(api_key=settings.gemini_api_key)
        self.model = genai.GenerativeModel(settings.gemini_model)
        
        # Agent routing map
        self.agent_routing_map = {
            "file_management": "FileManagerAgent",
            "page_management": "PageManagerAgent", 
            "frame_layout": "FrameLayoutAgent",
            "vector_editing": "VectorEditingAgent",
            "component_management": "ComponentManagerAgent",
            "prototyping": "PrototypeAgent",
            "export": "ExportAgent",
            "plugin_management": "PluginManagerAgent",
            "collaboration": "CollaborationAgent",
            "comments": "CommentAgent",
            "version_control": "VersionControlAgent",
            "team_management": "TeamManagementAgent",
            "image_management": "ImageAgent",
            "content_addition": "AddAgent",
            "banner_creation": "BannerAgent",
            "api_integration": "APIIntegrationAgent"
        }
        
        self.capabilities = [
            "intent_classification",
            "task_routing",
            "multi_agent_coordination",
            "natural_language_processing"
        ]
        
        # Intent classification prompt template
        self.intent_prompt = self._build_intent_prompt()
    
    def _build_intent_prompt(self) -> str:
        """Build the intent classification prompt for Gemini."""
        return """
You are an expert Figma design assistant that classifies user intents and routes tasks to appropriate specialist agents.

Given a user query about Figma operations, classify the intent and provide routing information.

Available Agent Categories:
- file_management: Create, open, delete, organize Figma files
- page_management: Create, rename, delete, reorder pages
- frame_layout: Layout systems, frames, auto-layout, grids, constraints
- vector_editing: Shapes, paths, pen tool, boolean operations
- component_management: Components, variants, instances, design systems
- prototyping: Interactive flows, transitions, overlays
- export: Asset export, formats, settings, batch operations
- plugin_management: Install, run, manage plugins
- collaboration: Real-time editing, live cursors, multiplayer features
- comments: Comments, annotations, feedback systems
- version_control: History, versions, restore, branching
- team_management: Roles, permissions, invitations
- image_management: Upload, manage image assets
- content_addition: Add shapes, icons, quick elements
- banner_creation: Create and insert banners with design and layout
- api_integration: Direct Figma REST API operations

Respond with a JSON object containing:
{
    "intent_category": "category_name",
    "confidence": 0.0-1.0,
    "task_type": "specific_task_description",
    "parameters": {"key": "value"},
    "requires_multiple_agents": boolean,
    "agent_sequence": ["agent1", "agent2"] if multiple agents needed,
    "reasoning": "explanation of classification"
}

User Query: {query}
"""
    
    async def can_handle(self, task: str) -> bool:
        """Router agent can handle any task for classification."""
        return True
    
    async def classify_intent(self, user_query: str) -> Dict[str, Any]:
        """
        Classify user intent using Gemini LLM.
        
        Args:
            user_query: Natural language query from user
            
        Returns:
            Dict[str, Any]: Intent classification result
        """
        try:
            # Prepare prompt
            prompt = self.intent_prompt.format(query=user_query)
            
            # Configure safety settings
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
            
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                safety_settings=safety_settings,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,  # Low temperature for consistent classification
                    max_output_tokens=1000,
                    response_mime_type="application/json"
                )
            )
            
            # Parse JSON response
            result = json.loads(response.text)
            
            # Validate required fields
            required_fields = ["intent_category", "confidence", "task_type"]
            for field in required_fields:
                if field not in result:
                    raise ValueError(f"Missing required field: {field}")
            
            logger.info(f"Classified intent: {result['intent_category']} (confidence: {result['confidence']})")
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse Gemini response as JSON: {e}")
            raise TaskExecutionError(
                "Failed to parse intent classification response",
                task_type="intent_classification",
                execution_stage="response_parsing"
            )
        except Exception as e:
            logger.error(f"Intent classification failed: {e}")
            raise TaskExecutionError(
                f"Intent classification failed: {str(e)}",
                task_type="intent_classification",
                execution_stage="llm_generation"
            )
    
    async def route_to_agent(self, intent_result: Dict[str, Any]) -> str:
        """
        Route task to appropriate agent based on intent classification.
        
        Args:
            intent_result: Result from intent classification
            
        Returns:
            str: Target agent name
        """
        intent_category = intent_result.get("intent_category")
        
        if intent_category not in self.agent_routing_map:
            logger.warning(f"Unknown intent category: {intent_category}")
            # Default to API integration agent for unknown intents
            return self.agent_routing_map["api_integration"]
        
        target_agent = self.agent_routing_map[intent_category]
        logger.info(f"Routing to agent: {target_agent}")
        return target_agent
    
    async def execute_single_agent_task(
        self,
        agent_name: str,
        task_params: Dict[str, Any]
    ) -> AgentResponse:
        """
        Execute task with a single specialist agent.
        
        Args:
            agent_name: Name of the target agent
            task_params: Task parameters
            
        Returns:
            AgentResponse: Execution result
        """
        # Get agent instance
        agent = AgentFactory.get_agent(agent_name)
        if not agent:
            raise AgentError(f"Agent {agent_name} not found or not initialized")
        
        # Create task
        task = AgentTask(
            task_type=task_params.get("task_type", "unknown"),
            description=task_params.get("description", ""),
            parameters=task_params.get("parameters", {}),
            context=task_params.get("context", {})
        )
        
        # Execute task
        return await agent.execute_with_timeout(task)
    
    async def execute_multi_agent_sequence(
        self,
        agent_sequence: List[str],
        task_params: Dict[str, Any]
    ) -> AgentResponse:
        """
        Execute task sequence across multiple agents.
        
        Args:
            agent_sequence: Ordered list of agent names
            task_params: Task parameters
            
        Returns:
            AgentResponse: Combined execution result
        """
        results = []
        context = task_params.get("context", {})
        
        for i, agent_name in enumerate(agent_sequence):
            try:
                # Update context with previous results
                if results:
                    context["previous_results"] = results
                
                # Execute with current agent
                agent_params = {
                    **task_params,
                    "context": context,
                    "sequence_position": i,
                    "total_agents": len(agent_sequence)
                }
                
                result = await self.execute_single_agent_task(agent_name, agent_params)
                results.append({
                    "agent": agent_name,
                    "result": result.to_dict()
                })
                
                # If any agent fails, stop the sequence
                if not result.success:
                    return AgentResponse(
                        success=False,
                        error_message=f"Multi-agent sequence failed at {agent_name}: {result.error_message}",
                        metadata={"partial_results": results}
                    )
                
            except Exception as e:
                logger.error(f"Multi-agent sequence failed at {agent_name}: {e}")
                return AgentResponse(
                    success=False,
                    error_message=f"Multi-agent sequence failed at {agent_name}: {str(e)}",
                    metadata={"partial_results": results}
                )
        
        return AgentResponse(
            success=True,
            result=results,
            metadata={"sequence_completed": True, "agents_used": agent_sequence}
        )
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        """
        Execute routing task - classify intent and dispatch to appropriate agents.
        
        Args:
            task: Routing task containing user query
            
        Returns:
            AgentResponse: Routing and execution result
        """
        try:
            user_query = task.parameters.get("query", "")
            if not user_query:
                return AgentResponse(
                    success=False,
                    error_message="No user query provided for routing"
                )
            
            # Step 1: Classify intent
            intent_result = await self.classify_intent(user_query)
            
            # Step 2: Check if multiple agents are needed
            if intent_result.get("requires_multiple_agents", False):
                agent_sequence = intent_result.get("agent_sequence", [])
                if not agent_sequence:
                    return AgentResponse(
                        success=False,
                        error_message="Multi-agent task specified but no agent sequence provided"
                    )
                
                # Execute multi-agent sequence
                task_params = {
                    "task_type": intent_result["task_type"],
                    "description": user_query,
                    "parameters": intent_result.get("parameters", {}),
                    "context": {**task.context, "intent_result": intent_result}
                }
                
                return await self.execute_multi_agent_sequence(agent_sequence, task_params)
            
            else:
                # Step 3: Route to single agent
                target_agent = await self.route_to_agent(intent_result)
                
                task_params = {
                    "task_type": intent_result["task_type"],
                    "description": user_query,
                    "parameters": intent_result.get("parameters", {}),
                    "context": {**task.context, "intent_result": intent_result}
                }
                
                return await self.execute_single_agent_task(target_agent, task_params)
        
        except Exception as e:
            logger.error(f"Router agent execution failed: {e}")
            return AgentResponse(
                success=False,
                error_message=f"Router agent execution failed: {str(e)}"
            )
    
    async def get_available_tools(self) -> List:
        """Router agent doesn't expose MCP tools directly."""
        return []


# Register the router agent
AgentFactory.register_agent(RouterAgent, "RouterAgent")
