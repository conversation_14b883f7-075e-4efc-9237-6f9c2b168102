#!/usr/bin/env python3
"""
Comprehensive import test to verify all modules can be imported correctly.
"""

def test_all_imports():
    """Test all critical imports in the application."""
    
    print("🔍 Testing All Critical Imports")
    print("=" * 50)
    
    # Test core imports
    print("\n1. Testing Core Module Imports...")
    try:
        from app.core.config import settings
        print("   ✅ app.core.config imported successfully")
        
        from app.core.exceptions import FigmaMCPError, AgentError
        print("   ✅ app.core.exceptions imported successfully")
        
        from app.core.mcp_client import mcp_client_manager
        print("   ✅ app.core.mcp_client imported successfully")
        
    except Exception as e:
        print(f"   ❌ Core imports failed: {e}")
        return False
    
    # Test agent imports
    print("\n2. Testing Agent Module Imports...")
    try:
        from app.agents import (
            BaseAgent, AgentResponse, AgentFactory, AgentTask, RouterAgent
        )
        print("   ✅ Base agent classes imported successfully")
        
        from app.agents.file_manager import FileManagerAgent
        from app.agents.page_manager import PageManagerAgent
        from app.agents.frame_layout import FrameLayoutAgent
        from app.agents.vector_editing import VectorEditingAgent
        print("   ✅ Design agents imported successfully")
        
        from app.agents.component_manager import ComponentManagerAgent
        from app.agents.prototype import PrototypeAgent
        print("   ✅ Component and prototype agents imported successfully")
        
        from app.agents.export import ExportAgent, PluginManagerAgent
        print("   ✅ Export and plugin agents imported successfully")
        
        from app.agents.collaboration import (
            CommentAgent, CollaborationAgent, 
            VersionControlAgent, TeamManagementAgent
        )
        print("   ✅ Collaboration agents imported successfully")
        
        from app.agents.content import (
            ImageAgent, AddAgent, BannerAgent, APIIntegrationAgent
        )
        print("   ✅ Content agents imported successfully")
        
    except Exception as e:
        print(f"   ❌ Agent imports failed: {e}")
        return False
    
    # Test API imports
    print("\n3. Testing API Module Imports...")
    try:
        from app.api.endpoints import app
        print("   ✅ FastAPI app imported successfully")
        
    except Exception as e:
        print(f"   ❌ API imports failed: {e}")
        return False
    
    # Test frontend imports
    print("\n4. Testing Frontend Dependencies...")
    try:
        import streamlit as st
        import plotly.express as px
        import plotly.graph_objects as go
        import pandas as pd
        import numpy as np
        import httpx
        print("   ✅ All frontend dependencies imported successfully")
        
    except Exception as e:
        print(f"   ❌ Frontend imports failed: {e}")
        return False
    
    # Test agent factory functionality
    print("\n5. Testing Agent Factory...")
    try:
        # Check registered agents
        available_agents = AgentFactory.list_available_agents()
        active_agents = AgentFactory.list_active_agents()
        
        print(f"   📊 Available agent classes: {len(available_agents)}")
        print(f"   🤖 Active agent instances: {len(active_agents)}")
        
        if len(active_agents) >= 17:
            print("   ✅ All agents properly registered and active")
        else:
            print(f"   ⚠️  Expected 17 agents, found {len(active_agents)}")
            
    except Exception as e:
        print(f"   ❌ Agent factory test failed: {e}")
        return False
    
    # Test configuration
    print("\n6. Testing Configuration...")
    try:
        print(f"   🌐 API Host: {settings.api_host}")
        print(f"   🔌 API Port: {settings.api_port}")
        print(f"   🎨 Streamlit Port: {settings.streamlit_port}")
        print(f"   📝 Log Level: {settings.log_level}")
        print("   ✅ Configuration loaded successfully")
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All Import Tests Passed!")
    print("✅ Project structure is correct")
    print("✅ All __init__.py files present")
    print("✅ Python path configuration working")
    print("✅ All dependencies available")
    print("✅ Agent factory operational")
    print("✅ Configuration system working")
    
    return True

if __name__ == "__main__":
    test_all_imports()
