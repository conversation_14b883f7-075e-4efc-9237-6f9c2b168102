"""
Agent modules for the Figma MCP client.

This package contains all the specialist agents that handle different aspects
of Figma operations through the MCP protocol.
"""

from .base import BaseAgent, AgentResponse, AgentFactory, AgentTask
from .router import RouterAgent
from .file_manager import FileManagerAgent
from .page_manager import PageManagerAgent
from .frame_layout import FrameLayoutAgent
from .vector_editing import VectorEditingAgent
from .component_manager import ComponentManagerAgent
from .prototype import PrototypeAgent
from .export import ExportAgent, PluginManagerAgent
from .collaboration import (
    CommentAgent, CollaborationAgent,
    VersionControlAgent, TeamManagementAgent
)
from .content import (
    ImageAgent, AddAgent, BannerAgent, APIIntegrationAgent
)

__all__ = [
    "BaseAgent", "AgentResponse", "AgentFactory", "AgentTask", "RouterAgent",
    "FileManagerAgent", "PageManagerAgent", "FrameLayoutAgent",
    "VectorEditingAgent", "ComponentManagerAgent", "PrototypeAgent",
    "ExportAgent", "PluginManagerAgent", "CommentAgent",
    "CollaborationAgent", "VersionControlAgent", "TeamManagementAgent",
    "ImageAgent", "AddAgent", "BannerAgent", "APIIntegrationAgent"
]
