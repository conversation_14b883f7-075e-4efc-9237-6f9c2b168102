"""
Component Manager Agent for the Figma MCP client.

This agent handles design system management including components, variants,
instances, and component libraries through MCP tools.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
import json

from mcp import StdioServerParameters
from mcp.types import Tool, TextContent

from .base import BaseAgent, AgentTask, AgentResponse, AgentFactory
from ..core.config import settings
from ..core.mcp_client import mcp_client_manager
from ..core.exceptions import AgentError, TaskExecutionError


logger = logging.getLogger(__name__)


class ComponentManagerAgent(BaseAgent):
    """
    Specialist agent for Figma component and design system management.
    
    Handles component creation, variant management, instance creation,
    and design system operations through the Figma MCP server.
    """
    
    def __init__(self):
        super().__init__(
            name="ComponentManagerAgent",
            description="Manages Figma design system including components, variants, and instances"
        )
        
        self.capabilities = [
            "component_creation",
            "variant_management",
            "instance_creation",
            "design_system",
            "component_library",
            "component_properties",
            "component_publishing",
            "component_organization"
        ]
        
        # MCP server parameters for Figma component operations
        self.figma_server_params = StdioServerParameters(
            command="figma-mcp-server",
            args=["--component-operations"],
            env={"FIGMA_ACCESS_TOKEN": settings.figma_access_token} if settings.figma_access_token else None
        )
    
    async def can_handle(self, task: str) -> bool:
        """Check if this agent can handle the given task."""
        component_operations = [
            "create_component", "component", "master_component",
            "variant", "component_variant", "create_variant",
            "instance", "create_instance", "component_instance",
            "design_system", "component_library", "library",
            "publish_component", "component_properties",
            "detach_instance", "swap_instance"
        ]
        
        return any(op in task.lower() for op in component_operations)
    
    async def get_available_tools(self) -> List[Tool]:
        """Get available MCP tools for component operations."""
        try:
            async with mcp_client_manager.get_connection(
                "figma-component-server", 
                self.figma_server_params
            ) as session:
                tools_response = await session.list_tools()
                return tools_response.tools
        except Exception as e:
            logger.error(f"Failed to get component management tools: {e}")
            return []
    
    async def create_component(
        self,
        file_id: str,
        node_id: str,
        name: str,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a component from an existing node.
        
        Args:
            file_id: ID of the Figma file
            node_id: ID of the node to convert to component
            name: Name of the component
            description: Optional component description
            
        Returns:
            Dict[str, Any]: Component creation result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-component-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_component",
                    arguments={
                        "file_id": file_id,
                        "node_id": node_id,
                        "name": name,
                        "description": description or ""
                    }
                )
                
                logger.info(f"Created component '{name}' from node {node_id}")
                return {
                    "success": True,
                    "component_id": result.content[0].text if result.content else None,
                    "component_name": name,
                    "description": description,
                    "source_node": node_id
                }
                
        except Exception as e:
            logger.error(f"Failed to create component {name}: {e}")
            raise TaskExecutionError(
                f"Failed to create component: {str(e)}",
                task_type="create_component",
                execution_stage="mcp_call"
            )
    
    async def create_variant(
        self,
        file_id: str,
        component_id: str,
        variant_properties: Dict[str, str],
        node_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a variant of an existing component.
        
        Args:
            file_id: ID of the Figma file
            component_id: ID of the main component
            variant_properties: Properties that define this variant
            node_id: Optional node ID to use for the variant
            
        Returns:
            Dict[str, Any]: Variant creation result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-component-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_variant",
                    arguments={
                        "file_id": file_id,
                        "component_id": component_id,
                        "variant_properties": variant_properties,
                        "node_id": node_id
                    }
                )
                
                logger.info(f"Created variant for component {component_id}")
                return {
                    "success": True,
                    "variant_id": result.content[0].text if result.content else None,
                    "component_id": component_id,
                    "properties": variant_properties
                }
                
        except Exception as e:
            logger.error(f"Failed to create variant for component {component_id}: {e}")
            raise TaskExecutionError(
                f"Failed to create variant: {str(e)}",
                task_type="create_variant",
                execution_stage="mcp_call"
            )
    
    async def create_instance(
        self,
        file_id: str,
        page_id: str,
        component_id: str,
        x: float = 0,
        y: float = 0,
        overrides: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create an instance of a component.
        
        Args:
            file_id: ID of the Figma file
            page_id: ID of the page to place instance on
            component_id: ID of the component to instantiate
            x: X position for the instance
            y: Y position for the instance
            overrides: Property overrides for the instance
            
        Returns:
            Dict[str, Any]: Instance creation result
        """
        try:
            overrides = overrides or {}
            
            async with mcp_client_manager.get_connection(
                "figma-component-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_instance",
                    arguments={
                        "file_id": file_id,
                        "page_id": page_id,
                        "component_id": component_id,
                        "x": x,
                        "y": y,
                        "overrides": overrides
                    }
                )
                
                logger.info(f"Created instance of component {component_id}")
                return {
                    "success": True,
                    "instance_id": result.content[0].text if result.content else None,
                    "component_id": component_id,
                    "position": {"x": x, "y": y},
                    "overrides": overrides
                }
                
        except Exception as e:
            logger.error(f"Failed to create instance of component {component_id}: {e}")
            raise TaskExecutionError(
                f"Failed to create instance: {str(e)}",
                task_type="create_instance",
                execution_stage="mcp_call"
            )
    
    async def publish_component(
        self,
        file_id: str,
        component_id: str,
        library_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Publish a component to a library.
        
        Args:
            file_id: ID of the Figma file
            component_id: ID of the component to publish
            library_name: Optional name of the library
            
        Returns:
            Dict[str, Any]: Publishing result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-component-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "publish_component",
                    arguments={
                        "file_id": file_id,
                        "component_id": component_id,
                        "library_name": library_name
                    }
                )
                
                logger.info(f"Published component {component_id} to library")
                return {
                    "success": True,
                    "component_id": component_id,
                    "library_name": library_name,
                    "published": True
                }
                
        except Exception as e:
            logger.error(f"Failed to publish component {component_id}: {e}")
            raise TaskExecutionError(
                f"Failed to publish component: {str(e)}",
                task_type="publish_component",
                execution_stage="mcp_call"
            )
    
    async def set_component_properties(
        self,
        file_id: str,
        component_id: str,
        properties: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Set properties for a component.
        
        Args:
            file_id: ID of the Figma file
            component_id: ID of the component
            properties: Properties to set
            
        Returns:
            Dict[str, Any]: Properties setting result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-component-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "set_component_properties",
                    arguments={
                        "file_id": file_id,
                        "component_id": component_id,
                        "properties": properties
                    }
                )
                
                logger.info(f"Set properties for component {component_id}")
                return {
                    "success": True,
                    "component_id": component_id,
                    "properties": properties
                }
                
        except Exception as e:
            logger.error(f"Failed to set properties for component {component_id}: {e}")
            raise TaskExecutionError(
                f"Failed to set component properties: {str(e)}",
                task_type="set_component_properties",
                execution_stage="mcp_call"
            )
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        """
        Execute component management task.
        
        Args:
            task: Task to execute
            
        Returns:
            AgentResponse: Execution result
        """
        try:
            task_type = task.task_type.lower()
            parameters = task.parameters
            
            if task_type in ["create_component", "component", "master_component"]:
                result = await self.create_component(
                    file_id=parameters.get("file_id", ""),
                    node_id=parameters.get("node_id", ""),
                    name=parameters.get("name", "Component"),
                    description=parameters.get("description")
                )
            elif task_type in ["variant", "component_variant", "create_variant"]:
                result = await self.create_variant(
                    file_id=parameters.get("file_id", ""),
                    component_id=parameters.get("component_id", ""),
                    variant_properties=parameters.get("variant_properties", {}),
                    node_id=parameters.get("node_id")
                )
            elif task_type in ["instance", "create_instance", "component_instance"]:
                result = await self.create_instance(
                    file_id=parameters.get("file_id", ""),
                    page_id=parameters.get("page_id", ""),
                    component_id=parameters.get("component_id", ""),
                    x=parameters.get("x", 0),
                    y=parameters.get("y", 0),
                    overrides=parameters.get("overrides")
                )
            elif task_type in ["publish_component", "publish"]:
                result = await self.publish_component(
                    file_id=parameters.get("file_id", ""),
                    component_id=parameters.get("component_id", ""),
                    library_name=parameters.get("library_name")
                )
            elif task_type == "component_properties":
                result = await self.set_component_properties(
                    file_id=parameters.get("file_id", ""),
                    component_id=parameters.get("component_id", ""),
                    properties=parameters.get("properties", {})
                )
            else:
                return AgentResponse(
                    success=False,
                    error_message=f"Unknown component operation: {task_type}"
                )
            
            return AgentResponse(
                success=True,
                result=result,
                metadata={
                    "agent": self.name,
                    "task_type": task_type,
                    "parameters": parameters
                }
            )
            
        except Exception as e:
            logger.error(f"Component manager agent execution failed: {e}")
            return AgentResponse(
                success=False,
                error_message=f"Component operation failed: {str(e)}"
            )


# Register the component manager agent
AgentFactory.register_agent(ComponentManagerAgent, "ComponentManagerAgent")
