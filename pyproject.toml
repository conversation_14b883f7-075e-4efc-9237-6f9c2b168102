[project]
name = "figma-mcp-client"
version = "0.1.0"
description = "Multi-Agent Figma MCP Client with FastAPI and Streamlit"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Core MCP and async support
    "mcp>=1.11.0",
    "asyncio-mqtt>=0.16.2",

    # Web frameworks
    "fastapi>=0.115.0",
    "streamlit>=1.40.0",
    "uvicorn[standard]>=0.32.0",

    # Google Gemini API
    "google-generativeai>=0.8.3",

    # Data validation and serialization
    "pydantic>=2.10.0",
    "pydantic-settings>=2.6.0",

    # HTTP client and async support
    "httpx>=0.28.0",
    "aiohttp>=3.11.0",

    # Environment and configuration
    "python-dotenv>=1.0.1",

    # Logging and monitoring
    "structlog>=24.4.0",
    "prometheus-client>=0.21.0",

    # Redis for task queuing and caching
    "redis[hiredis]>=5.2.0",

    # WebSocket support for real-time features
    "websockets>=14.1",

    # Image processing
    "Pillow>=11.0.0",

    # Data handling and visualization
    "pandas>=2.2.0",
    "plotly>=5.24.0",
    "numpy>=1.26.0",

    # Testing and development
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-mock>=3.14.0",
    "black>=24.10.0",
    "flake8>=7.1.0",
    "mypy>=1.13.0",
    "isort>=5.13.0",
]

[project.optional-dependencies]
dev = [
    "pytest-cov>=6.0.0",
    "pytest-xdist>=3.6.0",
    "pre-commit>=4.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
