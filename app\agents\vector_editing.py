"""
Vector Editing Agent for the Figma MCP client.

This agent handles vector operations including shapes, paths, pen tool,
and boolean operations through MCP tools.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
import json

from mcp import StdioServerParameters
from mcp.types import <PERSON><PERSON>, TextContent

from .base import BaseAgent, AgentTask, AgentResponse, AgentFactory
from ..core.config import settings
from ..core.mcp_client import mcp_client_manager
from ..core.exceptions import Agent<PERSON>rror, TaskExecutionError


logger = logging.getLogger(__name__)


class VectorEditingAgent(BaseAgent):
    """
    Specialist agent for Figma vector editing operations.
    
    Handles shape creation, path manipulation, pen tool operations,
    boolean operations, and vector transformations through the Figma MCP server.
    """
    
    def __init__(self):
        super().__init__(
            name="VectorEditingAgent",
            description="Manages Figma vector operations including shapes, paths, pen tool, and boolean operations"
        )
        
        self.capabilities = [
            "shape_creation",
            "path_editing",
            "pen_tool",
            "boolean_operations",
            "vector_transformations",
            "stroke_styling",
            "fill_styling",
            "curve_editing"
        ]
        
        # MCP server parameters for Figma vector operations
        self.figma_server_params = StdioServerParameters(
            command="figma-mcp-server",
            args=["--vector-operations"],
            env={"FIGMA_ACCESS_TOKEN": settings.figma_access_token} if settings.figma_access_token else None
        )
    
    async def can_handle(self, task: str) -> bool:
        """Check if this agent can handle the given task."""
        vector_operations = [
            "create_shape", "rectangle", "circle", "ellipse", "polygon",
            "path", "pen_tool", "bezier", "curve",
            "boolean", "union", "subtract", "intersect", "exclude",
            "stroke", "fill", "gradient", "transform",
            "rotate", "scale", "skew", "flip"
        ]
        
        return any(op in task.lower() for op in vector_operations)
    
    async def get_available_tools(self) -> List[Tool]:
        """Get available MCP tools for vector operations."""
        try:
            async with mcp_client_manager.get_connection(
                "figma-vector-server", 
                self.figma_server_params
            ) as session:
                tools_response = await session.list_tools()
                return tools_response.tools
        except Exception as e:
            logger.error(f"Failed to get vector editing tools: {e}")
            return []
    
    async def create_shape(
        self,
        file_id: str,
        page_id: str,
        shape_type: str,
        x: float,
        y: float,
        width: float,
        height: float,
        properties: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a basic shape.
        
        Args:
            file_id: ID of the Figma file
            page_id: ID of the page
            shape_type: Type of shape (RECTANGLE, ELLIPSE, POLYGON, STAR, etc.)
            x: X position
            y: Y position
            width: Shape width
            height: Shape height
            properties: Additional shape properties
            
        Returns:
            Dict[str, Any]: Shape creation result
        """
        try:
            properties = properties or {}
            
            async with mcp_client_manager.get_connection(
                "figma-vector-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_shape",
                    arguments={
                        "file_id": file_id,
                        "page_id": page_id,
                        "shape_type": shape_type,
                        "x": x,
                        "y": y,
                        "width": width,
                        "height": height,
                        "properties": properties
                    }
                )
                
                logger.info(f"Created {shape_type} shape in page {page_id}")
                return {
                    "success": True,
                    "shape_id": result.content[0].text if result.content else None,
                    "shape_type": shape_type,
                    "position": {"x": x, "y": y},
                    "size": {"width": width, "height": height}
                }
                
        except Exception as e:
            logger.error(f"Failed to create {shape_type} shape: {e}")
            raise TaskExecutionError(
                f"Failed to create shape: {str(e)}",
                task_type="create_shape",
                execution_stage="mcp_call"
            )
    
    async def create_path(
        self,
        file_id: str,
        page_id: str,
        path_data: str,
        stroke_properties: Optional[Dict[str, Any]] = None,
        fill_properties: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a custom path using SVG path data.
        
        Args:
            file_id: ID of the Figma file
            page_id: ID of the page
            path_data: SVG path data string
            stroke_properties: Stroke styling properties
            fill_properties: Fill styling properties
            
        Returns:
            Dict[str, Any]: Path creation result
        """
        try:
            stroke_properties = stroke_properties or {"color": "#000000", "width": 1}
            fill_properties = fill_properties or {"color": "#FFFFFF"}
            
            async with mcp_client_manager.get_connection(
                "figma-vector-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "create_path",
                    arguments={
                        "file_id": file_id,
                        "page_id": page_id,
                        "path_data": path_data,
                        "stroke_properties": stroke_properties,
                        "fill_properties": fill_properties
                    }
                )
                
                logger.info(f"Created custom path in page {page_id}")
                return {
                    "success": True,
                    "path_id": result.content[0].text if result.content else None,
                    "path_data": path_data,
                    "stroke": stroke_properties,
                    "fill": fill_properties
                }
                
        except Exception as e:
            logger.error(f"Failed to create path: {e}")
            raise TaskExecutionError(
                f"Failed to create path: {str(e)}",
                task_type="create_path",
                execution_stage="mcp_call"
            )
    
    async def boolean_operation(
        self,
        file_id: str,
        operation: str,
        node_ids: List[str]
    ) -> Dict[str, Any]:
        """
        Perform boolean operations on vector shapes.
        
        Args:
            file_id: ID of the Figma file
            operation: Boolean operation (UNION, SUBTRACT, INTERSECT, EXCLUDE)
            node_ids: List of node IDs to perform operation on
            
        Returns:
            Dict[str, Any]: Boolean operation result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-vector-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "boolean_operation",
                    arguments={
                        "file_id": file_id,
                        "operation": operation,
                        "node_ids": node_ids
                    }
                )
                
                logger.info(f"Performed {operation} boolean operation on {len(node_ids)} nodes")
                return {
                    "success": True,
                    "result_id": result.content[0].text if result.content else None,
                    "operation": operation,
                    "input_nodes": node_ids
                }
                
        except Exception as e:
            logger.error(f"Failed to perform boolean operation {operation}: {e}")
            raise TaskExecutionError(
                f"Failed to perform boolean operation: {str(e)}",
                task_type="boolean_operation",
                execution_stage="mcp_call"
            )
    
    async def apply_styling(
        self,
        file_id: str,
        node_id: str,
        stroke_properties: Optional[Dict[str, Any]] = None,
        fill_properties: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Apply stroke and fill styling to a vector shape.
        
        Args:
            file_id: ID of the Figma file
            node_id: ID of the node to style
            stroke_properties: Stroke styling properties
            fill_properties: Fill styling properties
            
        Returns:
            Dict[str, Any]: Styling application result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-vector-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "apply_styling",
                    arguments={
                        "file_id": file_id,
                        "node_id": node_id,
                        "stroke_properties": stroke_properties,
                        "fill_properties": fill_properties
                    }
                )
                
                logger.info(f"Applied styling to node {node_id}")
                return {
                    "success": True,
                    "node_id": node_id,
                    "stroke": stroke_properties,
                    "fill": fill_properties
                }
                
        except Exception as e:
            logger.error(f"Failed to apply styling to node {node_id}: {e}")
            raise TaskExecutionError(
                f"Failed to apply styling: {str(e)}",
                task_type="apply_styling",
                execution_stage="mcp_call"
            )
    
    async def transform_vector(
        self,
        file_id: str,
        node_id: str,
        transformation: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Apply transformations to a vector shape.
        
        Args:
            file_id: ID of the Figma file
            node_id: ID of the node to transform
            transformation: Transformation parameters (rotate, scale, skew, etc.)
            
        Returns:
            Dict[str, Any]: Transformation result
        """
        try:
            async with mcp_client_manager.get_connection(
                "figma-vector-server",
                self.figma_server_params
            ) as session:
                result = await session.call_tool(
                    "transform_vector",
                    arguments={
                        "file_id": file_id,
                        "node_id": node_id,
                        "transformation": transformation
                    }
                )
                
                logger.info(f"Applied transformation to node {node_id}")
                return {
                    "success": True,
                    "node_id": node_id,
                    "transformation": transformation
                }
                
        except Exception as e:
            logger.error(f"Failed to transform node {node_id}: {e}")
            raise TaskExecutionError(
                f"Failed to transform vector: {str(e)}",
                task_type="transform_vector",
                execution_stage="mcp_call"
            )
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        """
        Execute vector editing task.
        
        Args:
            task: Task to execute
            
        Returns:
            AgentResponse: Execution result
        """
        try:
            task_type = task.task_type.lower()
            parameters = task.parameters
            
            if task_type in ["create_shape", "rectangle", "circle", "ellipse", "polygon"]:
                shape_type = parameters.get("shape_type", task_type.upper())
                if task_type == "circle":
                    shape_type = "ELLIPSE"
                
                result = await self.create_shape(
                    file_id=parameters.get("file_id", ""),
                    page_id=parameters.get("page_id", ""),
                    shape_type=shape_type,
                    x=parameters.get("x", 0),
                    y=parameters.get("y", 0),
                    width=parameters.get("width", 100),
                    height=parameters.get("height", 100),
                    properties=parameters.get("properties")
                )
            elif task_type in ["path", "pen_tool", "bezier", "curve"]:
                result = await self.create_path(
                    file_id=parameters.get("file_id", ""),
                    page_id=parameters.get("page_id", ""),
                    path_data=parameters.get("path_data", ""),
                    stroke_properties=parameters.get("stroke_properties"),
                    fill_properties=parameters.get("fill_properties")
                )
            elif task_type in ["boolean", "union", "subtract", "intersect", "exclude"]:
                operation = parameters.get("operation", task_type.upper())
                if task_type == "boolean":
                    operation = parameters.get("operation", "UNION")
                
                result = await self.boolean_operation(
                    file_id=parameters.get("file_id", ""),
                    operation=operation,
                    node_ids=parameters.get("node_ids", [])
                )
            elif task_type in ["stroke", "fill", "gradient", "styling"]:
                result = await self.apply_styling(
                    file_id=parameters.get("file_id", ""),
                    node_id=parameters.get("node_id", ""),
                    stroke_properties=parameters.get("stroke_properties"),
                    fill_properties=parameters.get("fill_properties")
                )
            elif task_type in ["transform", "rotate", "scale", "skew", "flip"]:
                result = await self.transform_vector(
                    file_id=parameters.get("file_id", ""),
                    node_id=parameters.get("node_id", ""),
                    transformation=parameters.get("transformation", {})
                )
            else:
                return AgentResponse(
                    success=False,
                    error_message=f"Unknown vector operation: {task_type}"
                )
            
            return AgentResponse(
                success=True,
                result=result,
                metadata={
                    "agent": self.name,
                    "task_type": task_type,
                    "parameters": parameters
                }
            )
            
        except Exception as e:
            logger.error(f"Vector editing agent execution failed: {e}")
            return AgentResponse(
                success=False,
                error_message=f"Vector operation failed: {str(e)}"
            )


# Register the vector editing agent
AgentFactory.register_agent(VectorEditingAgent, "VectorEditingAgent")
