"""
Exception classes for the Figma MCP client.

This module defines custom exceptions for different error scenarios
in the multi-agent system.
"""

from typing import Optional, Dict, Any


class FigmaMCPError(Exception):
    """Base exception for all Figma MCP client errors."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class AgentError(FigmaMCPError):
    """Exception raised by agents during task execution."""
    
    def __init__(
        self,
        message: str,
        agent_name: Optional[str] = None,
        task_id: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.agent_name = agent_name
        self.task_id = task_id


class MCPConnectionError(FigmaMCPError):
    """Exception raised when MCP connection fails."""
    
    def __init__(
        self,
        message: str,
        server_url: Optional[str] = None,
        connection_type: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.server_url = server_url
        self.connection_type = connection_type


class MCPTimeoutError(MCPConnectionError):
    """Exception raised when MCP operations timeout."""
    
    def __init__(
        self,
        message: str,
        timeout_duration: Optional[float] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.timeout_duration = timeout_duration


class AgentNotFoundError(AgentError):
    """Exception raised when a requested agent is not found."""
    pass


class AgentBusyError(AgentError):
    """Exception raised when an agent is busy and cannot handle new tasks."""
    pass


class TaskExecutionError(AgentError):
    """Exception raised when task execution fails."""
    
    def __init__(
        self,
        message: str,
        task_type: Optional[str] = None,
        execution_stage: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.task_type = task_type
        self.execution_stage = execution_stage


class ConfigurationError(FigmaMCPError):
    """Exception raised for configuration-related errors."""
    pass


class AuthenticationError(FigmaMCPError):
    """Exception raised for authentication-related errors."""
    pass


class RateLimitError(FigmaMCPError):
    """Exception raised when rate limits are exceeded."""
    
    def __init__(
        self,
        message: str,
        retry_after: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after


class ValidationError(FigmaMCPError):
    """Exception raised for data validation errors."""
    
    def __init__(
        self,
        message: str,
        field_errors: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.field_errors = field_errors or {}


class CircuitBreakerError(FigmaMCPError):
    """Exception raised when circuit breaker is open."""
    
    def __init__(
        self,
        message: str,
        service_name: Optional[str] = None,
        failure_count: Optional[int] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.service_name = service_name
        self.failure_count = failure_count
