"""
Main entry point for the Figma MCP Multi-Agent Client.

This module provides the main FastAPI application and CLI entry points.
"""

import asyncio
import logging
import sys
from pathlib import Path

import uvicorn
from app.core.config import settings
from app.api.endpoints import app


# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)


def main():
    """Main entry point for the FastAPI application."""
    logger.info("Starting Figma MCP Multi-Agent Client")

    # Run the FastAPI application
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_debug,
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
