"""
Tests for the agent system.

This module contains tests to verify that the agent factory can create
and manage all agents correctly, and that the router agent can classify
intents and dispatch to appropriate specialist agents.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from app.agents import (
    AgentFactory, RouterAgent, BaseAgent, AgentTask, AgentResponse,
    FileManagerAgent, PageManagerAgent, FrameLayoutAgent
)
from app.core.exceptions import AgentError


class TestAgentFactory:
    """Test the AgentFactory functionality."""
    
    def test_agent_registration(self):
        """Test that agents can be registered with the factory."""
        # Clear any existing registrations
        AgentFactory._agents.clear()
        AgentFactory._instances.clear()
        
        # Register a test agent
        AgentFactory.register_agent(FileManagerAgent, "TestFileManager")
        
        assert "TestFileManager" in AgentFactory.list_available_agents()
    
    def test_agent_creation(self):
        """Test that agents can be created through the factory."""
        # Clear any existing registrations
        AgentFactory._agents.clear()
        AgentFactory._instances.clear()
        
        # Register and create agent
        AgentFactory.register_agent(FileManagerAgent, "FileManagerAgent")
        agent = AgentFactory.create_agent("FileManagerAgent")
        
        assert isinstance(agent, FileManagerAgent)
        assert agent.name == "FileManagerAgent"
        assert "FileManagerAgent" in AgentFactory.list_active_agents()
    
    def test_agent_retrieval(self):
        """Test that created agents can be retrieved."""
        # Clear any existing registrations
        AgentFactory._agents.clear()
        AgentFactory._instances.clear()
        
        # Register and create agent
        AgentFactory.register_agent(PageManagerAgent, "PageManagerAgent")
        original_agent = AgentFactory.create_agent("PageManagerAgent")
        
        # Retrieve agent
        retrieved_agent = AgentFactory.get_agent("PageManagerAgent")
        
        assert retrieved_agent is original_agent
    
    def test_agent_not_found(self):
        """Test error handling when agent is not found."""
        # Clear any existing registrations
        AgentFactory._agents.clear()
        AgentFactory._instances.clear()
        
        with pytest.raises(AgentError):
            AgentFactory.create_agent("NonExistentAgent")
    
    def test_status_summary(self):
        """Test that status summary works correctly."""
        # Clear any existing registrations
        AgentFactory._agents.clear()
        AgentFactory._instances.clear()
        
        # Register and create multiple agents
        AgentFactory.register_agent(FileManagerAgent, "FileManagerAgent")
        AgentFactory.register_agent(PageManagerAgent, "PageManagerAgent")
        
        agent1 = AgentFactory.create_agent("FileManagerAgent")
        agent2 = AgentFactory.create_agent("PageManagerAgent")
        
        summary = AgentFactory.get_agent_status_summary()
        
        assert summary["total_registered"] == 2
        assert summary["total_active"] == 2
        assert summary["busy_agents"] == 0
        assert "FileManagerAgent" in summary["agents"]
        assert "PageManagerAgent" in summary["agents"]


class TestBaseAgent:
    """Test the BaseAgent functionality."""
    
    @pytest.fixture
    def mock_agent(self):
        """Create a mock agent for testing."""
        class MockAgent(BaseAgent):
            def __init__(self):
                super().__init__("MockAgent", "Test agent")
                self.capabilities = ["test_capability"]
            
            async def can_handle(self, task: str) -> bool:
                return "test" in task.lower()
            
            async def execute(self, task: AgentTask) -> AgentResponse:
                return AgentResponse(
                    success=True,
                    result={"message": "Test completed"},
                    execution_time=0.1
                )
            
            async def get_available_tools(self):
                return []
        
        return MockAgent()
    
    @pytest.mark.asyncio
    async def test_task_validation(self, mock_agent):
        """Test task validation."""
        valid_task = AgentTask(task_type="test_task", description="Test task")
        invalid_task = AgentTask(task_type="invalid_task", description="Invalid task")
        
        assert await mock_agent.validate_task(valid_task) == True
        assert await mock_agent.validate_task(invalid_task) == False
    
    @pytest.mark.asyncio
    async def test_task_execution(self, mock_agent):
        """Test task execution with timeout."""
        task = AgentTask(task_type="test_task", description="Test task")
        
        response = await mock_agent.execute_with_timeout(task, timeout=5.0)
        
        assert response.success == True
        assert response.result["message"] == "Test completed"
        assert response.execution_time is not None
    
    @pytest.mark.asyncio
    async def test_agent_busy_error(self, mock_agent):
        """Test that busy agent raises error."""
        task1 = AgentTask(task_type="test_task", description="First task")
        task2 = AgentTask(task_type="test_task", description="Second task")
        
        # Start first task (make agent busy)
        mock_agent.is_busy = True
        
        with pytest.raises(Exception):  # Should raise AgentBusyError
            await mock_agent.execute_with_timeout(task2)
    
    def test_agent_status(self, mock_agent):
        """Test agent status reporting."""
        status = mock_agent.get_status()
        
        assert status["name"] == "MockAgent"
        assert status["description"] == "Test agent"
        assert status["is_busy"] == False
        assert status["capabilities"] == ["test_capability"]
        assert status["total_tasks"] == 0


class TestRouterAgent:
    """Test the RouterAgent functionality."""
    
    @pytest.fixture
    def router_agent(self):
        """Create a router agent for testing."""
        with patch('google.generativeai.configure'):
            with patch('google.generativeai.GenerativeModel'):
                return RouterAgent()
    
    @pytest.mark.asyncio
    async def test_can_handle_any_task(self, router_agent):
        """Test that router agent can handle any task."""
        assert await router_agent.can_handle("create file") == True
        assert await router_agent.can_handle("delete page") == True
        assert await router_agent.can_handle("random task") == True
    
    @pytest.mark.asyncio
    async def test_intent_classification(self, router_agent):
        """Test intent classification with mocked Gemini response."""
        mock_response = Mock()
        mock_response.text = '{"intent_category": "file_management", "confidence": 0.9, "task_type": "create_file", "parameters": {"name": "test.fig"}}'
        
        with patch.object(router_agent.model, 'generate_content', return_value=mock_response):
            result = await router_agent.classify_intent("Create a new Figma file")
            
            assert result["intent_category"] == "file_management"
            assert result["confidence"] == 0.9
            assert result["task_type"] == "create_file"
    
    def test_agent_routing(self, router_agent):
        """Test routing to appropriate agents."""
        intent_result = {"intent_category": "file_management"}
        target_agent = asyncio.run(router_agent.route_to_agent(intent_result))
        
        assert target_agent == "FileManagerAgent"
        
        intent_result = {"intent_category": "page_management"}
        target_agent = asyncio.run(router_agent.route_to_agent(intent_result))
        
        assert target_agent == "PageManagerAgent"
    
    def test_unknown_intent_routing(self, router_agent):
        """Test routing for unknown intents."""
        intent_result = {"intent_category": "unknown_category"}
        target_agent = asyncio.run(router_agent.route_to_agent(intent_result))
        
        # Should default to API integration agent
        assert target_agent == "APIIntegrationAgent"


class TestSpecialistAgents:
    """Test specialist agent functionality."""
    
    @pytest.mark.asyncio
    async def test_file_manager_can_handle(self):
        """Test FileManagerAgent task handling."""
        agent = FileManagerAgent()
        
        assert await agent.can_handle("create_file") == True
        assert await agent.can_handle("delete_file") == True
        assert await agent.can_handle("search_files") == True
        assert await agent.can_handle("create_component") == False
    
    @pytest.mark.asyncio
    async def test_page_manager_can_handle(self):
        """Test PageManagerAgent task handling."""
        agent = PageManagerAgent()
        
        assert await agent.can_handle("create_page") == True
        assert await agent.can_handle("rename_page") == True
        assert await agent.can_handle("list_pages") == True
        assert await agent.can_handle("create_file") == False
    
    @pytest.mark.asyncio
    async def test_frame_layout_can_handle(self):
        """Test FrameLayoutAgent task handling."""
        agent = FrameLayoutAgent()
        
        assert await agent.can_handle("create_frame") == True
        assert await agent.can_handle("auto_layout") == True
        assert await agent.can_handle("grid") == True
        assert await agent.can_handle("constraints") == True
        assert await agent.can_handle("create_file") == False


class TestIntegration:
    """Integration tests for the complete system."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_task_routing(self):
        """Test complete task routing from query to execution."""
        # Clear factory state
        AgentFactory._agents.clear()
        AgentFactory._instances.clear()
        
        # Register agents
        AgentFactory.register_agent(FileManagerAgent, "FileManagerAgent")
        AgentFactory.register_agent(RouterAgent, "RouterAgent")
        
        # Create router
        with patch('google.generativeai.configure'):
            with patch('google.generativeai.GenerativeModel'):
                router = AgentFactory.create_agent("RouterAgent")
        
        # Mock the intent classification
        mock_response = Mock()
        mock_response.text = '{"intent_category": "file_management", "confidence": 0.9, "task_type": "create_file", "parameters": {"name": "test.fig"}, "requires_multiple_agents": false}'
        
        with patch.object(router.model, 'generate_content', return_value=mock_response):
            # Mock the file manager execution
            file_manager = AgentFactory.create_agent("FileManagerAgent")
            
            with patch.object(file_manager, 'execute_with_timeout', return_value=AgentResponse(success=True, result={"file_id": "123"})):
                # Create and execute task
                task = AgentTask(
                    task_type="route_and_execute",
                    description="Create a new Figma file",
                    parameters={"query": "Create a new Figma file"}
                )
                
                response = await router.execute_with_timeout(task)
                
                assert response.success == True
                assert response.result is not None


if __name__ == "__main__":
    pytest.main([__file__])
