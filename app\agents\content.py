"""
Content Agents for the Figma MCP client.

This module contains specialized content agents including Image, Add, Banner,
and API Integration agents for content creation and management.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
import json

from mcp import StdioServerParameters
from mcp.types import Tool

from .base import BaseAgent, AgentTask, AgentResponse, AgentFactory
from ..core.config import settings
from ..core.mcp_client import mcp_client_manager
from ..core.exceptions import TaskExecutionError


logger = logging.getLogger(__name__)


class ImageAgent(BaseAgent):
    """Specialist agent for image management operations."""
    
    def __init__(self):
        super().__init__(
            name="ImageAgent",
            description="Manages image assets and operations"
        )
        
        self.capabilities = ["image_upload", "image_management", "asset_optimization"]
        
        self.figma_server_params = StdioServerParameters(
            command="figma-mcp-server",
            args=["--image-operations"],
            env={"FIGMA_ACCESS_TOKEN": settings.figma_access_token} if settings.figma_access_token else None
        )
    
    async def can_handle(self, task: str) -> bool:
        return any(op in task.lower() for op in ["image", "upload", "asset", "photo", "picture"])
    
    async def get_available_tools(self) -> List[Tool]:
        try:
            async with mcp_client_manager.get_connection("figma-image-server", self.figma_server_params) as session:
                tools_response = await session.list_tools()
                return tools_response.tools
        except Exception as e:
            logger.error(f"Failed to get image tools: {e}")
            return []
    
    async def upload_image(self, file_id: str, image_data: str, name: str) -> Dict[str, Any]:
        try:
            async with mcp_client_manager.get_connection("figma-image-server", self.figma_server_params) as session:
                result = await session.call_tool("upload_image", arguments={
                    "file_id": file_id, "image_data": image_data, "name": name
                })
                
                logger.info(f"Uploaded image {name} to file {file_id}")
                return {"success": True, "image_id": result.content[0].text if result.content else None}
        except Exception as e:
            raise TaskExecutionError(f"Failed to upload image: {str(e)}", task_type="upload_image", execution_stage="mcp_call")
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        try:
            parameters = task.parameters
            if "upload" in task.task_type.lower() or "image" in task.task_type.lower():
                result = await self.upload_image(
                    file_id=parameters.get("file_id", ""),
                    image_data=parameters.get("image_data", ""),
                    name=parameters.get("name", "Image")
                )
            else:
                return AgentResponse(success=False, error_message=f"Unknown image operation: {task.task_type}")
            
            return AgentResponse(success=True, result=result, metadata={"agent": self.name, "task_type": task.task_type})
        except Exception as e:
            return AgentResponse(success=False, error_message=f"Image operation failed: {str(e)}")


class AddAgent(BaseAgent):
    """Specialist agent for adding quick elements and content."""
    
    def __init__(self):
        super().__init__(
            name="AddAgent",
            description="Adds shapes, icons, and quick elements"
        )
        
        self.capabilities = ["quick_add", "element_creation", "icon_insertion"]
        
        self.figma_server_params = StdioServerParameters(
            command="figma-mcp-server",
            args=["--add-operations"],
            env={"FIGMA_ACCESS_TOKEN": settings.figma_access_token} if settings.figma_access_token else None
        )
    
    async def can_handle(self, task: str) -> bool:
        return any(op in task.lower() for op in ["add", "insert", "create", "icon", "element"])
    
    async def get_available_tools(self) -> List[Tool]:
        try:
            async with mcp_client_manager.get_connection("figma-add-server", self.figma_server_params) as session:
                tools_response = await session.list_tools()
                return tools_response.tools
        except Exception as e:
            logger.error(f"Failed to get add tools: {e}")
            return []
    
    async def add_element(self, file_id: str, page_id: str, element_type: str, properties: Dict[str, Any]) -> Dict[str, Any]:
        try:
            async with mcp_client_manager.get_connection("figma-add-server", self.figma_server_params) as session:
                result = await session.call_tool("add_element", arguments={
                    "file_id": file_id, "page_id": page_id, "element_type": element_type, "properties": properties
                })
                
                logger.info(f"Added {element_type} element to page {page_id}")
                return {"success": True, "element_id": result.content[0].text if result.content else None}
        except Exception as e:
            raise TaskExecutionError(f"Failed to add element: {str(e)}", task_type="add_element", execution_stage="mcp_call")
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        try:
            parameters = task.parameters
            if "add" in task.task_type.lower() or "create" in task.task_type.lower():
                result = await self.add_element(
                    file_id=parameters.get("file_id", ""),
                    page_id=parameters.get("page_id", ""),
                    element_type=parameters.get("element_type", "rectangle"),
                    properties=parameters.get("properties", {})
                )
            else:
                return AgentResponse(success=False, error_message=f"Unknown add operation: {task.task_type}")
            
            return AgentResponse(success=True, result=result, metadata={"agent": self.name, "task_type": task.task_type})
        except Exception as e:
            return AgentResponse(success=False, error_message=f"Add operation failed: {str(e)}")


class BannerAgent(BaseAgent):
    """Specialist agent for banner creation and design."""
    
    def __init__(self):
        super().__init__(
            name="BannerAgent",
            description="Creates and manages banners with design and layout"
        )
        
        self.capabilities = ["banner_creation", "banner_design", "layout_optimization"]
        
        self.figma_server_params = StdioServerParameters(
            command="figma-mcp-server",
            args=["--banner-operations"],
            env={"FIGMA_ACCESS_TOKEN": settings.figma_access_token} if settings.figma_access_token else None
        )
    
    async def can_handle(self, task: str) -> bool:
        return any(op in task.lower() for op in ["banner", "header", "promotional", "advertisement"])
    
    async def get_available_tools(self) -> List[Tool]:
        try:
            async with mcp_client_manager.get_connection("figma-banner-server", self.figma_server_params) as session:
                tools_response = await session.list_tools()
                return tools_response.tools
        except Exception as e:
            logger.error(f"Failed to get banner tools: {e}")
            return []
    
    async def create_banner(self, file_id: str, page_id: str, banner_config: Dict[str, Any]) -> Dict[str, Any]:
        try:
            async with mcp_client_manager.get_connection("figma-banner-server", self.figma_server_params) as session:
                result = await session.call_tool("create_banner", arguments={
                    "file_id": file_id, "page_id": page_id, "banner_config": banner_config
                })
                
                logger.info(f"Created banner in page {page_id}")
                return {"success": True, "banner_id": result.content[0].text if result.content else None}
        except Exception as e:
            raise TaskExecutionError(f"Failed to create banner: {str(e)}", task_type="create_banner", execution_stage="mcp_call")
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        try:
            parameters = task.parameters
            if "banner" in task.task_type.lower() or "create" in task.task_type.lower():
                result = await self.create_banner(
                    file_id=parameters.get("file_id", ""),
                    page_id=parameters.get("page_id", ""),
                    banner_config=parameters.get("banner_config", {})
                )
            else:
                return AgentResponse(success=False, error_message=f"Unknown banner operation: {task.task_type}")
            
            return AgentResponse(success=True, result=result, metadata={"agent": self.name, "task_type": task.task_type})
        except Exception as e:
            return AgentResponse(success=False, error_message=f"Banner operation failed: {str(e)}")


class APIIntegrationAgent(BaseAgent):
    """Specialist agent for direct Figma REST API operations."""
    
    def __init__(self):
        super().__init__(
            name="APIIntegrationAgent",
            description="Handles direct Figma REST API operations not available through MCP"
        )
        
        self.capabilities = ["direct_api", "rest_operations", "advanced_queries"]
        
        # This agent uses direct HTTP calls instead of MCP
        self.api_base_url = "https://api.figma.com/v1"
    
    async def can_handle(self, task: str) -> bool:
        return any(op in task.lower() for op in ["api", "direct", "rest", "http", "advanced"])
    
    async def get_available_tools(self) -> List[Tool]:
        # This agent doesn't use MCP tools, it makes direct API calls
        return []
    
    async def direct_api_call(self, endpoint: str, method: str = "GET", data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        try:
            import httpx
            
            headers = {
                "X-Figma-Token": settings.figma_access_token,
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                url = f"{self.api_base_url}/{endpoint.lstrip('/')}"
                
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=data)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                response.raise_for_status()
                
                logger.info(f"Direct API call to {endpoint} successful")
                return {"success": True, "data": response.json(), "status_code": response.status_code}
                
        except Exception as e:
            raise TaskExecutionError(f"Direct API call failed: {str(e)}", task_type="direct_api_call", execution_stage="http_request")
    
    async def execute(self, task: AgentTask) -> AgentResponse:
        try:
            parameters = task.parameters
            result = await self.direct_api_call(
                endpoint=parameters.get("endpoint", ""),
                method=parameters.get("method", "GET"),
                data=parameters.get("data")
            )
            
            return AgentResponse(success=True, result=result, metadata={"agent": self.name, "task_type": task.task_type})
        except Exception as e:
            return AgentResponse(success=False, error_message=f"API integration operation failed: {str(e)}")


# Register all content agents
AgentFactory.register_agent(ImageAgent, "ImageAgent")
AgentFactory.register_agent(AddAgent, "AddAgent")
AgentFactory.register_agent(BannerAgent, "BannerAgent")
AgentFactory.register_agent(APIIntegrationAgent, "APIIntegrationAgent")
